import { json, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useFetcher } from "@remix-run/react";
import { getSession } from "~/session.server";
import { useEffect, useState } from "react";

// Mock data and types for dashboard
type CustomerSummary = {
  totalCustomers: number;
  activeContracts: number;
  vipCustomers: number;
};

type DeviceStatus = {
  online: number;
  needsMaintenance: number;
  offline: number;
};

type PerformanceMetrics = {
  avgEfficiency: number;
  energySaved: number;
  uptime: number;
};

type ServiceOrder = {
  id: string;
  customerName: string;
  serviceType: string;
};

type LoaderData = {
  customerSummary: CustomerSummary;
  recentOrders: ServiceOrder[];
  deviceStatus: DeviceStatus;
  performanceMetrics: PerformanceMetrics;
};

// Mock implementation of useMCP hook
const useMCP = (serverName: string) => {
  return {
    useTool: async (toolName: string, params: any) => {
      console.log(`MCP Tool call: ${toolName}`, params);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Return mock AI insights
      return {
        trends_analysis: `
          <h2>🌟 AI-Powered HVAC Insights</h2>
          <p>Based on recent analysis of industry trends, we recommend:</p>
          <ul>
            <li>Implement IoT sensors for real-time monitoring of HVAC systems</li>
            <li>Upgrade to energy-efficient models to reduce costs by 25%</li>
            <li>Schedule predictive maintenance for critical systems next quarter</li>
          </ul>
          <p><strong>Market Trend:</strong> Smart HVAC adoption increased by 42% in Q1 2025</p>
        `
      };
    }
  };
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const session = await getSession(request);
  const userId = session.get("userId");
  
  if (!userId) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  // Return mock dashboard data
  return json<LoaderData>({
    customerSummary: {
      totalCustomers: 142,
      activeContracts: 89,
      vipCustomers: 23
    },
    recentOrders: [
      { id: "SO-001", customerName: "ABC Manufacturing", serviceType: "Preventive Maintenance" },
      { id: "SO-002", customerName: "XYZ Corporation", serviceType: "Emergency Repair" },
      { id: "SO-003", customerName: "City Hospital", serviceType: "System Upgrade" },
      { id: "SO-004", customerName: "Grand Hotel", serviceType: "Filter Replacement" },
      { id: "SO-005", customerName: "Tech Campus", serviceType: "Performance Audit" }
    ],
    deviceStatus: {
      online: 287,
      needsMaintenance: 42,
      offline: 15
    },
    performanceMetrics: {
      avgEfficiency: 92.5,
      energySaved: 12500,
      uptime: 99.2
    }
  });
};

export default function EnhancedCRMDashboard() {
  const data = useLoaderData<typeof loader>();
  const [aiInsights, setAiInsights] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { useTool } = useMCP("python-mixer-server");

  // Fetch AI insights on component mount
  useEffect(() => {
    const fetchInsights = async () => {
      setIsLoading(true);
      try {
        const response = await useTool("research_hvac_trends", {
          query: "HVAC efficiency optimization",
          framework: "crewai"
        });

        if (response?.trends_analysis) {
          setAiInsights(response.trends_analysis);
        }
      } catch (error) {
        console.error("Failed to fetch AI insights:", error);
        setAiInsights("❌ Failed to load AI insights. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchInsights();
  }, [useTool]);

  // Handle error state
  if ("error" in data) {
    return (
      <div className="p-6">
        <h1 className="text-3xl font-bold mb-6">Enhanced CRM Dashboard</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <strong>Error:</strong> {data.error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-3xl font-bold mb-6">Enhanced CRM Dashboard</h1>
      
      {/* AI Insights Section */}
      <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
        <h2 className="text-xl font-bold mb-4">🌟 AI-Powered HVAC Insights</h2>
        {isLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-2">Analyzing HVAC trends with AI...</p>
          </div>
        ) : aiInsights ? (
          <div 
            className="prose max-w-none" 
            dangerouslySetInnerHTML={{ __html: aiInsights }} 
          />
        ) : (
          <p>No insights available</p>
        )}
      </div>

      {/* Dashboard metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Customer Summary Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Customer Summary</h2>
          <p>Total Customers: {data.customerSummary.totalCustomers}</p>
          <p>Active Contracts: {data.customerSummary.activeContracts}</p>
          <p>VIP Customers: {data.customerSummary.vipCustomers}</p>
        </div>

        {/* Device Status Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Device Status</h2>
          <p>Online: {data.deviceStatus.online}</p>
          <p>Needs Maintenance: {data.deviceStatus.needsMaintenance}</p>
          <p>Offline: {data.deviceStatus.offline}</p>
        </div>

        {/* Performance Metrics Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Performance Metrics</h2>
          <p>Avg Efficiency: {data.performanceMetrics.avgEfficiency}%</p>
          <p>Energy Saved: {data.performanceMetrics.energySaved} kWh</p>
          <p>Uptime: {data.performanceMetrics.uptime}%</p>
        </div>

        {/* Recent Activity Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-bold mb-4">Recent Service Orders</h2>
          <ul>
            {data.recentOrders.map(order => (
              <li key={order.id} className="mb-2">
                {order.customerName} - {order.serviceType}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
