package config

import (
	"log"
	"sync"

	"github.com/spf13/viper"
)

var (
	once     sync.Once
	instance *Config
)

type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Python   PythonConfig
	Frontend FrontendConfig
}

type ServerConfig struct {
	Port        string
	Environment string
	LogLevel    string
	EnableCORS  bool
}

type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

type PythonConfig struct {
	MixerAPIHost string
	MixerAPIPort string
}

type FrontendConfig struct {
	HVACRemixHost string
	HVACRemixPort string
}

func Load() *Config {
	once.Do(func() {
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		viper.AddConfigPath(".")
		viper.AddConfigPath("./config")
		viper.AutomaticEnv()

		// Set default values
		viper.SetDefault("server.port", "8080")
		viper.SetDefault("server.environment", "development")
		viper.SetDefault("server.loglevel", "info")
		viper.SetDefault("server.enablecors", true)
		viper.SetDefault("database.host", "localhost")
		viper.SetDefault("database.port", "5432")
		viper.SetDefault("database.sslmode", "disable")

		if err := viper.ReadInConfig(); err != nil {
			log.Printf("Warning: Could not read config file: %v", err)
		}

		instance = &Config{
			Server: ServerConfig{
				Port:        viper.GetString("server.port"),
				Environment: viper.GetString("server.environment"),
				LogLevel:    viper.GetString("server.loglevel"),
				EnableCORS:  viper.GetBool("server.enablecors"),
			},
			Database: DatabaseConfig{
				Host:     viper.GetString("database.host"),
				Port:     viper.GetString("database.port"),
				User:     viper.GetString("database.user"),
				Password: viper.GetString("database.password"),
				DBName:   viper.GetString("database.dbname"),
				SSLMode:  viper.GetString("database.sslmode"),
			},
			Python: PythonConfig{
				MixerAPIHost: viper.GetString("python.mixer_api_host"),
				MixerAPIPort: viper.GetString("python.mixer_api_port"),
			},
			Frontend: FrontendConfig{
				HVACRemixHost: viper.GetString("frontend.hvac_remix_host"),
				HVACRemixPort: viper.GetString("frontend.hvac_remix_port"),
			},
		}
	})

	return instance
}
