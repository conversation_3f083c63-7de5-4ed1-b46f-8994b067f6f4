package services

import (
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

type DashboardClient struct {
	conn *websocket.Conn
	send chan []byte
}

type RealtimeDashboard struct {
	clients    map[*DashboardClient]bool
	register   chan *DashboardClient
	unregister chan *DashboardClient
	broadcast  chan interface{}
	mu         sync.Mutex
}

func NewRealtimeDashboard() *RealtimeDashboard {
	return &RealtimeDashboard{
		clients:    make(map[*DashboardClient]bool),
		register:   make(chan *DashboardClient),
		unregister: make(chan *DashboardClient),
		broadcast:  make(chan interface{}, 100),
	}
}

func (rd *RealtimeDashboard) Run() {
	for {
		select {
		case client := <-rd.register:
			rd.mu.Lock()
			rd.clients[client] = true
			rd.mu.Unlock()
			log.Println("Client connected to dashboard")

		case client := <-rd.unregister:
			rd.mu.Lock()
			if _, ok := rd.clients[client]; ok {
				delete(rd.clients, client)
				close(client.send)
			}
			rd.mu.Unlock()
			log.Println("Client disconnected from dashboard")

		case message := <-rd.broadcast:
			data, err := json.Marshal(message)
			if err != nil {
				log.Printf("Error marshalling dashboard message: %v", err)
				continue
			}

			rd.mu.Lock()
			for client := range rd.clients {
				select {
				case client.send <- data:
				default:
					close(client.send)
					delete(rd.clients, client)
				}
			}
			rd.mu.Unlock()
		}
	}
}

func (rd *RealtimeDashboard) BroadcastSystemStatus() {
	go func() {
		for {
			status := map[string]interface{}{
				"timestamp":   time.Now().Unix(),
				"cpu":         getCPUUsage(),
				"memory":      getMemoryUsage(),
				"activeJobs":  getActiveJobCount(),
				"throughput":  getSystemThroughput(),
				"temperature": getSystemTemperature(),
			}
			rd.broadcast <- status
			time.Sleep(5 * time.Second)
		}
	}()
}

// Placeholder functions - implement with actual metrics
func getCPUUsage() float64          { return 0.35 }
func getMemoryUsage() float64       { return 0.42 }
func getActiveJobCount() int        { return 12 }
func getSystemThroughput() float64  { return 125.7 }
func getSystemTemperature() float64 { return 42.5 }
