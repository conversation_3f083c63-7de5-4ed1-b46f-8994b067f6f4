package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// MCPClient handles communication with the Python Mixer MCP server
type MCPClient struct {
	baseURL    string
	httpClient *http.Client
}

// NewMCPClient creates a new MCP client instance
func NewMCPClient(baseURL string) *MCPClient {
	return &MCPClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// ResearchHVACTrends performs HVAC industry research using the MCP server
func (c *MCPClient) ResearchHVACTrends(ctx context.Context, query string, framework string) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"query":     query,
		"framework": framework,
	}
	return c.postRequest(ctx, "/mcp/research_hvac_trends", payload)
}

// ProcessTranscription processes audio transcription using the MCP server
func (c *MCPClient) ProcessTranscription(ctx context.Context, audioPath, framework, analysisType string) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"audio_path":    audioPath,
		"framework":     framework,
		"analysis_type": analysisType,
	}
	return c.postRequest(ctx, "/mcp/process_transcription", payload)
}

// AnalyzeData analyzes HVAC data using multiple AI frameworks via MCP
func (c *MCPClient) AnalyzeData(ctx context.Context, data string, frameworks []string) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"data":       data,
		"frameworks": frameworks,
	}
	return c.postRequest(ctx, "/mcp/analyze_data", payload)
}

// GetMixerStatus retrieves the current status of the Cosmic AI Mixer
func (c *MCPClient) GetMixerStatus(ctx context.Context) (map[string]interface{}, error) {
	return c.getRequest(ctx, "/mcp/mixer_status")
}

// Helper function for POST requests
func (c *MCPClient) postRequest(ctx context.Context, endpoint string, payload interface{}) (map[string]interface{}, error) {
	url := c.baseURL + endpoint

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling payload: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	return c.parseResponse(resp)
}

// Helper function for GET requests
func (c *MCPClient) getRequest(ctx context.Context, endpoint string) (map[string]interface{}, error) {
	url := c.baseURL + endpoint

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending request: %w", err)
	}
	defer resp.Body.Close()

	return c.parseResponse(resp)
}

// Helper function to parse JSON responses
func (c *MCPClient) parseResponse(resp *http.Response) (map[string]interface{}, error) {
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	return result, nil
}
