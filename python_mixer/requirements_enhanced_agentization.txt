# Enhanced Agentization System Requirements
# ========================================
# Multi-framework AI agent system for HVAC CRM

# Core AI Frameworks
pydantic-ai>=0.0.13
crewai>=0.70.1
langchain>=0.3.0
langchain-community>=0.3.0
langchain-openai>=0.2.0

# Data Validation & Models
pydantic>=2.8.0
pydantic-settings>=2.4.0

# Database Drivers
pymongo>=4.8.0
redis>=5.0.0
psycopg2-binary>=2.9.9
motor>=3.5.0  # Async MongoDB

# API Framework
fastapi>=0.115.0
uvicorn>=0.30.0

# AI/ML Libraries
openai>=1.40.0
anthropic>=0.34.0
sentence-transformers>=3.0.0
numpy>=1.26.0

# Audio Processing (for M4A transcriptions)
librosa>=0.10.0
soundfile>=0.12.0
pydub>=0.25.0

# Email Processing
imapclient>=3.0.0
email-validator>=2.2.0

# Utilities
python-dotenv>=1.0.0
aiofiles>=24.1.0
asyncio-mqtt>=0.16.0
celery>=5.4.0

# Monitoring & Logging
structlog>=24.4.0
prometheus-client>=0.20.0

# Development & Testing
pytest>=8.3.0
pytest-asyncio>=0.24.0
black>=24.8.0
isort>=5.13.0