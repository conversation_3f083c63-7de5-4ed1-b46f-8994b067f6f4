# 🌟 Unified Cosmic Python Mixer Dockerfile
# Multi-stage build for optimized production image

# Stage 1: Build stage with UV for fast dependency installation
FROM python:3.11-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install UV for fast Python package management
RUN pip install uv

# Set working directory
WORKDIR /app

# Copy dependency files
COPY requirements_enhanced_2025.txt requirements.txt pyproject.toml ./

# Install Python dependencies with UV
RUN uv pip install --system -r requirements_enhanced_2025.txt

# Stage 2: Production stage
FROM python:3.11-slim as production

# Install runtime system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    ffmpeg \
    libsndfile1 \
    libsndfile1-dev \
    libasound2-dev \
    portaudio19-dev \
    libportaudio2 \
    libportaudiocpp0 \
    libav-tools \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r hvac && useradd -r -g hvac hvac

# Set working directory
WORKDIR /app

# Copy Python packages from builder stage
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p \
    /app/data \
    /app/logs \
    /app/config \
    /app/audio_temp \
    /app/audio_processed \
    /app/documents \
    /app/exports \
    /app/nemo_models \
    /app/nemo_cache \
    /app/whisper_models

# Set proper permissions
RUN chown -R hvac:hvac /app

# Create health check script
RUN echo '#!/bin/bash\ncurl -f http://localhost:7860/health || exit 1' > /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# Environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV GRADIO_SERVER_NAME=0.0.0.0
ENV GRADIO_SERVER_PORT=7860
ENV GRADIO_TEMP_DIR=/app/audio_temp

# Expose ports
EXPOSE 7860 8000

# Switch to non-root user
USER hvac

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=120s --retries=3 \
    CMD /app/healthcheck.sh

# Default command
CMD ["python", "unified_cosmic_interface.py"]
