#!/usr/bin/env python3
"""
🌟 UNIFIED COSMIC INTERFACE 2025 🌟
===================================

Ultimate unified interface combining all Python mixer functionalities
with enhanced cosmic design, Tavily MCP integration, and NVIDIA STT.

Features:
- 🌟 Cosmic design with glassmorphism and animations
- 🔍 Tavily MCP real-time research integration
- 🎤 Enhanced NVIDIA NeMo transcription
- 📧 Advanced email analysis with AI frameworks
- 📄 Document processing with OCR and AI
- 📅 Calendar management with AI scheduling
- 🧾 Invoice processing with AI extraction
- 🤖 Multi-framework AI comparison (CrewAI, PydanticAI, LangChain)
- 🐳 Docker containerization ready
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import gradio as gr
from loguru import logger

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import cosmic components
from gradio_components.cosmic_styles import CosmicStyles
from gradio_components.tavily_research_component import TavilyResearchComponent
from gradio_components.enhanced_transcription import EnhancedTranscriptionComponent

# Import existing components (enhanced)
try:
    from gradio_components.email_analysis import EmailAnalysisComponent
    from gradio_components.document_processor import DocumentProcessorComponent
    from gradio_components.calendar_management import CalendarManagementComponent
    from gradio_components.invoice_processing import InvoiceProcessingComponent
    EMAIL_COMPONENTS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Some components not available: {e}")
    EMAIL_COMPONENTS_AVAILABLE = False

# Import enhanced AI mixer
try:
    from enhanced_cosmic_ai_mixer_2025 import MultiFrameworkAIProcessor, AIFramework
    AI_FRAMEWORKS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"AI frameworks not available: {e}")
    AI_FRAMEWORKS_AVAILABLE = False


class UnifiedCosmicInterface:
    """
    🌟 Unified Cosmic Interface 2025 - Ultimate Python Mixer Interface
    
    Combines all functionalities in a single, beautiful, cosmic-designed interface.
    """
    
    def __init__(self):
        # Initialize cosmic styles
        self.cosmic_styles = CosmicStyles()
        
        # Initialize components
        self.tavily_research = TavilyResearchComponent()
        self.enhanced_transcription = EnhancedTranscriptionComponent()
        
        # Initialize AI processor if available
        if AI_FRAMEWORKS_AVAILABLE:
            self.ai_processor = MultiFrameworkAIProcessor()
        else:
            self.ai_processor = None
        
        # Initialize existing components if available
        if EMAIL_COMPONENTS_AVAILABLE:
            self.email_analysis = EmailAnalysisComponent()
            self.document_processor = DocumentProcessorComponent()
            self.calendar_management = CalendarManagementComponent()
            self.invoice_processing = InvoiceProcessingComponent()
        
        # System state
        self.system_stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "start_time": datetime.now()
        }
        
        logger.info("🌟 Unified Cosmic Interface 2025 initialized successfully!")
    
    def create_interface(self) -> gr.Blocks:
        """
        Create the unified cosmic interface.
        
        Returns:
            gr.Blocks: Complete Gradio interface
        """
        # Get cosmic CSS
        cosmic_css = self.cosmic_styles.get_cosmic_css()
        
        # Add component-specific CSS
        cosmic_css += self.cosmic_styles.get_component_css("transcription")
        cosmic_css += self.cosmic_styles.get_component_css("tavily_research")
        
        with gr.Blocks(
            title="🌟 Unified Cosmic Interface 2025",
            theme=self.cosmic_styles.get_cosmic_theme(),
            css=cosmic_css
        ) as interface:
            
            # Cosmic Header
            gr.HTML(self.cosmic_styles.get_cosmic_header(
                "🌟 Unified Cosmic Interface 2025"
            ))
            
            # System Status Dashboard
            with gr.Row():
                with gr.Column(scale=3):
                    system_status = gr.HTML(
                        value=self._get_system_status_html(),
                        elem_classes=["cosmic-card"]
                    )
                with gr.Column(scale=1):
                    refresh_status_btn = gr.Button(
                        "🔄 Refresh Status",
                        elem_classes=["cosmic-button-secondary"],
                        size="lg"
                    )
            
            # Main Interface Tabs
            with gr.Tabs():
                
                # 🔍 Tavily Research Tab
                with gr.Tab("🔍 Tavily Research", elem_id="research-tab"):
                    research_group, research_outputs = self.tavily_research.create_research_interface()
                
                # 🎤 Enhanced Transcription Tab
                with gr.Tab("🎤 Enhanced Transcription", elem_id="transcription-tab"):
                    transcription_group, transcription_outputs = self.enhanced_transcription.create_transcription_interface()
                
                # 📧 Email Analysis Tab (if available)
                if EMAIL_COMPONENTS_AVAILABLE:
                    with gr.Tab("📧 Email Analysis", elem_id="email-tab"):
                        self._create_email_analysis_tab()
                
                # 📄 Document Processing Tab (if available)
                if EMAIL_COMPONENTS_AVAILABLE:
                    with gr.Tab("📄 Document Processing", elem_id="document-tab"):
                        self._create_document_processing_tab()
                
                # 📅 Calendar Management Tab (if available)
                if EMAIL_COMPONENTS_AVAILABLE:
                    with gr.Tab("📅 Calendar Management", elem_id="calendar-tab"):
                        self._create_calendar_management_tab()
                
                # 🧾 Invoice Processing Tab (if available)
                if EMAIL_COMPONENTS_AVAILABLE:
                    with gr.Tab("🧾 Invoice Processing", elem_id="invoice-tab"):
                        self._create_invoice_processing_tab()
                
                # 🤖 AI Framework Comparison Tab
                if AI_FRAMEWORKS_AVAILABLE:
                    with gr.Tab("🤖 AI Framework Comparison", elem_id="ai-comparison-tab"):
                        self._create_ai_comparison_tab()
                
                # ⚙️ System Management Tab
                with gr.Tab("⚙️ System Management", elem_id="system-tab"):
                    self._create_system_management_tab()
            
            # Event handlers
            refresh_status_btn.click(
                fn=self._refresh_system_status,
                outputs=[system_status]
            )
            
            # Footer
            gr.HTML("""
            <div style="text-align: center; margin-top: 40px; padding: 32px; 
                        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
                        backdrop-filter: blur(20px); border-radius: 24px; border: 1px solid rgba(255,255,255,0.2);">
                <h3 style="color: #667eea; margin-bottom: 16px;">🌟 Unified Cosmic Interface 2025</h3>
                <p style="opacity: 0.8; margin-bottom: 12px;">
                    Powered by Tavily MCP, NVIDIA NeMo STT, CrewAI, PydanticAI, LangChain & Advanced AI Frameworks
                </p>
                <p style="opacity: 0.6; font-size: 0.9rem;">
                    <strong>Version:</strong> 2.0.0 | <strong>Date:</strong> 2025-05-05 | 
                    <strong>Uptime:</strong> <span id="uptime">Calculating...</span>
                </p>
            </div>
            """)
        
        return interface
    
    def _create_email_analysis_tab(self):
        """Create enhanced email analysis tab."""
        gr.HTML("""
        <div class="processing-card">
            <h3 style="color: #ea4335; font-size: 1.5rem; margin-bottom: 16px;">
                📧 Enhanced Email Analysis with AI
            </h3>
            <p style="opacity: 0.8;">
                Advanced email processing with multi-framework AI analysis and Tavily research
            </p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                email_input = gr.Textbox(
                    label="📧 Email Content",
                    placeholder="Paste email content here...",
                    lines=8,
                    elem_classes=["cosmic-input"]
                )
                
                with gr.Row():
                    ai_framework = gr.Dropdown(
                        choices=["native", "crewai", "pydantic_ai", "langchain"],
                        value="native",
                        label="🤖 AI Framework",
                        elem_classes=["cosmic-input"]
                    )
                    
                    include_research = gr.Checkbox(
                        label="🔍 Include Tavily Research",
                        value=True,
                        elem_classes=["cosmic-input"]
                    )
                
                analyze_email_btn = gr.Button(
                    "🚀 Analyze Email",
                    elem_classes=["cosmic-button-primary"],
                    size="lg"
                )
            
            with gr.Column(scale=2):
                email_analysis_results = gr.Markdown(
                    label="📊 Analysis Results",
                    elem_classes=["cosmic-card"]
                )
                
                email_research_results = gr.Markdown(
                    label="🔍 Research Insights",
                    elem_classes=["cosmic-card"]
                )
    
    def _create_document_processing_tab(self):
        """Create enhanced document processing tab."""
        gr.HTML("""
        <div class="processing-card">
            <h3 style="color: #1a73e8; font-size: 1.5rem; margin-bottom: 16px;">
                📄 Enhanced Document Processing
            </h3>
            <p style="opacity: 0.8;">
                AI-powered document analysis with OCR, classification, and research
            </p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                document_upload = gr.File(
                    label="📁 Upload Document",
                    file_types=[".pdf", ".docx", ".txt", ".jpg", ".png"],
                    elem_classes=["upload-area"]
                )
                
                process_document_btn = gr.Button(
                    "🚀 Process Document",
                    elem_classes=["cosmic-button-primary"],
                    size="lg"
                )
            
            with gr.Column(scale=2):
                document_analysis = gr.Markdown(
                    label="📊 Document Analysis",
                    elem_classes=["cosmic-card"]
                )
    
    def _create_calendar_management_tab(self):
        """Create enhanced calendar management tab."""
        gr.HTML("""
        <div class="processing-card">
            <h3 style="color: #fbbc05; font-size: 1.5rem; margin-bottom: 16px;">
                📅 Enhanced Calendar Management
            </h3>
            <p style="opacity: 0.8;">
                AI-powered scheduling optimization and calendar intelligence
            </p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                calendar_upload = gr.File(
                    label="📁 Upload Calendar Data (CSV)",
                    file_types=[".csv"],
                    elem_classes=["upload-area"]
                )
                
                process_calendar_btn = gr.Button(
                    "🚀 Process Calendar",
                    elem_classes=["cosmic-button-primary"],
                    size="lg"
                )
            
            with gr.Column(scale=2):
                calendar_analysis = gr.Markdown(
                    label="📊 Calendar Analysis",
                    elem_classes=["cosmic-card"]
                )
    
    def _create_invoice_processing_tab(self):
        """Create enhanced invoice processing tab."""
        gr.HTML("""
        <div class="processing-card">
            <h3 style="color: #34a853; font-size: 1.5rem; margin-bottom: 16px;">
                🧾 Enhanced Invoice Processing
            </h3>
            <p style="opacity: 0.8;">
                AI-powered invoice analysis with OCR and data extraction
            </p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                invoice_upload = gr.File(
                    label="📁 Upload Invoice",
                    file_types=[".pdf", ".jpg", ".png"],
                    elem_classes=["upload-area"]
                )
                
                process_invoice_btn = gr.Button(
                    "🚀 Process Invoice",
                    elem_classes=["cosmic-button-primary"],
                    size="lg"
                )
            
            with gr.Column(scale=2):
                invoice_analysis = gr.Markdown(
                    label="📊 Invoice Analysis",
                    elem_classes=["cosmic-card"]
                )
    
    def _create_ai_comparison_tab(self):
        """Create AI framework comparison tab."""
        gr.HTML("""
        <div class="processing-card">
            <h3 style="color: #9c27b0; font-size: 1.5rem; margin-bottom: 16px;">
                🤖 AI Framework Performance Comparison
            </h3>
            <p style="opacity: 0.8;">
                Compare performance across CrewAI, PydanticAI, LangChain, and Native frameworks
            </p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=2):
                comparison_input = gr.Textbox(
                    label="📊 Data for Analysis",
                    placeholder="Enter data or text for multi-framework analysis...",
                    lines=6,
                    elem_classes=["cosmic-input"]
                )
                
                selected_frameworks = gr.CheckboxGroup(
                    choices=["native", "crewai", "pydantic_ai", "langchain"],
                    value=["native", "crewai"],
                    label="🔧 Select Frameworks",
                    elem_classes=["cosmic-input"]
                )
                
                compare_frameworks_btn = gr.Button(
                    "⚡ Compare Frameworks",
                    elem_classes=["cosmic-button-primary"],
                    size="lg"
                )
            
            with gr.Column(scale=2):
                framework_comparison = gr.Markdown(
                    label="📊 Framework Comparison",
                    elem_classes=["cosmic-card"]
                )
                
                performance_metrics = gr.JSON(
                    label="⚡ Performance Metrics",
                    elem_classes=["cosmic-card"]
                )
    
    def _create_system_management_tab(self):
        """Create system management tab."""
        gr.HTML("""
        <div class="processing-card">
            <h3 style="color: #ff9800; font-size: 1.5rem; margin-bottom: 16px;">
                ⚙️ System Management & Health Monitoring
            </h3>
            <p style="opacity: 0.8;">
                Real-time system monitoring, configuration, and performance analytics
            </p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # System controls
                restart_services_btn = gr.Button(
                    "🔄 Restart Services",
                    elem_classes=["cosmic-button-secondary"],
                    size="lg"
                )
                
                clear_cache_btn = gr.Button(
                    "🗑️ Clear Cache",
                    elem_classes=["cosmic-button-secondary"],
                    size="lg"
                )
                
                export_logs_btn = gr.Button(
                    "📄 Export Logs",
                    elem_classes=["cosmic-button-secondary"],
                    size="lg"
                )
            
            with gr.Column(scale=2):
                system_health = gr.Markdown(
                    label="🏥 System Health",
                    elem_classes=["cosmic-card"]
                )
                
                performance_stats = gr.JSON(
                    label="📈 Performance Statistics",
                    elem_classes=["cosmic-card"]
                )
    
    def _get_system_status_html(self) -> str:
        """Get system status HTML."""
        uptime = datetime.now() - self.system_stats["start_time"]
        
        return f"""
        <div class="cosmic-card">
            <h3 style="color: #667eea; margin-bottom: 20px;">🏥 System Status Dashboard</h3>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                <div class="metric-item">
                    <h4 style="color: #34a853; margin: 0;">✅ System Health</h4>
                    <p style="margin: 8px 0 0 0; font-size: 1.1rem;">Operational</p>
                </div>
                
                <div class="metric-item">
                    <h4 style="color: #1a73e8; margin: 0;">⏱️ Uptime</h4>
                    <p style="margin: 8px 0 0 0; font-size: 1.1rem;">{str(uptime).split('.')[0]}</p>
                </div>
                
                <div class="metric-item">
                    <h4 style="color: #fbbc05; margin: 0;">📊 Operations</h4>
                    <p style="margin: 8px 0 0 0; font-size: 1.1rem;">{self.system_stats['total_operations']}</p>
                </div>
                
                <div class="metric-item">
                    <h4 style="color: #ea4335; margin: 0;">🎯 Success Rate</h4>
                    <p style="margin: 8px 0 0 0; font-size: 1.1rem;">
                        {(self.system_stats['successful_operations'] / max(1, self.system_stats['total_operations']) * 100):.1f}%
                    </p>
                </div>
            </div>
            
            <div style="margin-top: 20px; padding: 16px; background: rgba(52, 168, 83, 0.1); border-radius: 12px;">
                <h4 style="color: #34a853; margin: 0 0 8px 0;">🌟 Available Components</h4>
                <p style="margin: 0; opacity: 0.8;">
                    ✅ Cosmic Styles • ✅ Tavily Research • ✅ Enhanced Transcription • 
                    {'✅' if EMAIL_COMPONENTS_AVAILABLE else '❌'} Email Components • 
                    {'✅' if AI_FRAMEWORKS_AVAILABLE else '❌'} AI Frameworks
                </p>
            </div>
        </div>
        """
    
    def _refresh_system_status(self) -> str:
        """Refresh and return updated system status."""
        return self._get_system_status_html()


def launch_unified_cosmic_interface():
    """Launch the unified cosmic interface."""
    logger.info("🚀 Launching Unified Cosmic Interface 2025...")
    
    try:
        # Create interface
        interface_manager = UnifiedCosmicInterface()
        interface = interface_manager.create_interface()
        
        # Launch with cosmic configuration
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=True,
            show_error=True,
            favicon_path=None,
            ssl_verify=False,
            quiet=False,
            show_tips=True,
            height=800,
            width="100%",
            inbrowser=True,
            prevent_thread_lock=False
        )
        
    except Exception as e:
        logger.error(f"Failed to launch Unified Cosmic Interface: {e}")
        raise


if __name__ == "__main__":
    launch_unified_cosmic_interface()
