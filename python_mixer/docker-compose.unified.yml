version: '3.8'

services:
  # 🌟 Unified Python Mixer - Main Application
  unified-python-mixer:
    build:
      context: .
      dockerfile: Dockerfile.unified
    container_name: hvac-unified-python-mixer
    environment:
      - PYTHONUNBUFFERED=1
      - GRADIO_SERVER_NAME=0.0.0.0
      - GRADIO_SERVER_PORT=7860
      - NEMO_SERVICE_URL=http://nemo-stt-polish:8765
      - REDIS_URL=redis://redis-unified:6379
      - POSTGRES_URL=**********************************************************/hvac_db
      - MONGODB_URL=***************************************************************
      - TAVILY_API_KEY=${TAVILY_API_KEY:-demo_key}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - DEBUG=true
      - LOG_LEVEL=INFO
    ports:
      - "7860:7860"  # Gradio interface
      - "8000:8000"  # API endpoints
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
      - ./audio_temp:/app/audio_temp
      - ./audio_processed:/app/audio_processed
      - ./documents:/app/documents
      - ./exports:/app/exports
    networks:
      - hvac-unified-network
    restart: unless-stopped
    depends_on:
      - redis-unified
      - postgres-unified
      - mongodb-unified
      - nemo-stt-polish
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # 🎤 NVIDIA NeMo STT Service for Polish
  nemo-stt-polish:
    image: nvcr.io/nvidia/nemo:24.01.speech
    container_name: hvac-nemo-stt-polish
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      - NEMO_MODEL=stt_pl_fastconformer_ctc_large
      - NEMO_LANGUAGE=pl
      - NEMO_BATCH_SIZE=1
      - NEMO_MAX_DURATION=300
      - PYTHONPATH=/workspace/NeMo
    ports:
      - "8765:8765"
    volumes:
      - ./nemo_models:/workspace/models
      - ./nemo_cache:/root/.cache
      - ./audio_temp:/workspace/audio_temp
      - ./nemo_config:/workspace/config
    networks:
      - hvac-unified-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    command: >
      bash -c "
        echo 'Starting NVIDIA NeMo STT Service for Polish...' &&
        python -c '
        import nemo.collections.asr as nemo_asr
        import torch
        from flask import Flask, request, jsonify
        import tempfile
        import os
        import json
        import logging
        
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        app = Flask(__name__)
        
        logger.info(\"Loading Polish FastConformer model...\")
        try:
            model = nemo_asr.models.EncDecCTCModel.from_pretrained(\"stt_pl_fastconformer_ctc_large\")
            model.eval()
            if torch.cuda.is_available():
                model = model.cuda()
            logger.info(\"Model loaded successfully\")
        except Exception as e:
            logger.error(f\"Failed to load model: {e}\")
            model = None
        
        @app.route(\"/health\", methods=[\"GET\"])
        def health():
            return jsonify({\"status\": \"healthy\", \"model_loaded\": model is not None})
        
        @app.route(\"/transcribe\", methods=[\"POST\"])
        def transcribe():
            if model is None:
                return jsonify({\"error\": \"Model not loaded\"}), 500
            
            try:
                audio_file = request.files.get(\"audio\")
                if not audio_file:
                    return jsonify({\"error\": \"No audio file provided\"}), 400
                
                config_str = request.form.get(\"config\", \"{}\")
                config = json.loads(config_str)
                
                with tempfile.NamedTemporaryFile(suffix=\".wav\", delete=False) as temp_file:
                    audio_file.save(temp_file.name)
                    temp_path = temp_file.name
                
                try:
                    transcription = model.transcribe([temp_path])
                    
                    if transcription and len(transcription) > 0:
                        text = transcription[0]
                        
                        if config.get(\"hvac_context\", False):
                            text = apply_hvac_corrections(text, config.get(\"keywords\", []))
                        
                        result = {
                            \"text\": text,
                            \"confidence\": 0.85,
                            \"language\": \"pl\",
                            \"model\": \"stt_pl_fastconformer_ctc_large\",
                            \"segments\": [{
                                \"start\": 0.0,
                                \"end\": 10.0,
                                \"text\": text,
                                \"confidence\": 0.85
                            }]
                        }
                        
                        return jsonify(result)
                    else:
                        return jsonify({\"error\": \"Transcription failed\"}), 500
                        
                finally:
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                        
            except Exception as e:
                logger.error(f\"Transcription error: {e}\")
                return jsonify({\"error\": str(e)}), 500
        
        def apply_hvac_corrections(text, keywords):
            corrections = {
                \"klimatyzator\": [\"klimatyzator\", \"klimatyzacja\"],
                \"split\": [\"split\", \"splitu\"],
                \"serwis\": [\"serwis\", \"service\"],
                \"montaż\": [\"montaż\", \"montażu\"],
                \"naprawa\": [\"naprawa\", \"naprawy\"],
                \"awaria\": [\"awaria\", \"awarii\"],
                \"LG\": [\"LG\", \"el dżi\", \"eldżi\"],
                \"Daikin\": [\"Daikin\", \"dajkin\"],
                \"Mitsubishi\": [\"Mitsubishi\", \"mitsubishi\"]
            }
            
            corrected_text = text
            for correct_form, variants in corrections.items():
                for variant in variants:
                    if variant != correct_form:
                        corrected_text = corrected_text.replace(variant, correct_form)
            
            return corrected_text
        
        logger.info(\"Starting Flask server on port 8765...\")
        app.run(host=\"0.0.0.0\", port=8765, debug=False)
        '"

  # 🗄️ PostgreSQL Database
  postgres-unified:
    image: postgres:15-alpine
    container_name: hvac-postgres-unified
    environment:
      - POSTGRES_DB=hvac_db
      - POSTGRES_USER=hvac_user
      - POSTGRES_PASSWORD=hvac_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5432:5432"
    volumes:
      - postgres_unified_data:/var/lib/postgresql/data
      - ./sql_init:/docker-entrypoint-initdb.d
    networks:
      - hvac-unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvac_user -d hvac_db"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 🍃 MongoDB Database
  mongodb-unified:
    image: mongo:7
    container_name: hvac-mongodb-unified
    environment:
      - MONGO_INITDB_ROOT_USERNAME=hvac_user
      - MONGO_INITDB_ROOT_PASSWORD=hvac_password
      - MONGO_INITDB_DATABASE=hvac_db
    ports:
      - "27017:27017"
    volumes:
      - mongodb_unified_data:/data/db
      - ./mongo_init:/docker-entrypoint-initdb.d
    networks:
      - hvac-unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 🔴 Redis Cache & Queue
  redis-unified:
    image: redis:7-alpine
    container_name: hvac-redis-unified
    ports:
      - "6379:6379"
    volumes:
      - redis_unified_data:/data
    networks:
      - hvac-unified-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 🔍 Alternative: Whisper STT (Fallback)
  whisper-stt:
    image: onerahmet/openai-whisper-asr-webservice:latest
    container_name: hvac-whisper-stt
    environment:
      - ASR_MODEL=large-v2
      - ASR_ENGINE=openai_whisper
    ports:
      - "9000:9000"
    volumes:
      - ./whisper_models:/root/.cache/whisper
    networks:
      - hvac-unified-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 📊 Monitoring & Analytics
  monitoring:
    image: grafana/grafana:latest
    container_name: hvac-monitoring
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=hvac_admin
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - hvac-unified-network
    restart: unless-stopped
    depends_on:
      - postgres-unified

  # 🌐 Nginx Reverse Proxy
  nginx-proxy:
    image: nginx:alpine
    container_name: hvac-nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    networks:
      - hvac-unified-network
    restart: unless-stopped
    depends_on:
      - unified-python-mixer

volumes:
  postgres_unified_data:
    driver: local
  mongodb_unified_data:
    driver: local
  redis_unified_data:
    driver: local
  grafana_data:
    driver: local

networks:
  hvac-unified-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
