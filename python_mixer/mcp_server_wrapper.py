#!/usr/bin/env python3
"""
MCP Server Wrapper for Enhanced Cosmic AI Mixer 2025
Exposes Python Mixer functionality via MCP protocol
"""

import os
import asyncio
import json
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from modelcontextprotocol import <PERSON><PERSON><PERSON><PERSON>
from enhanced_cosmic_ai_mixer_2025 import EnhancedCosmicAIMixer2025, AIFramework

app = FastAPI(
    title="Cosmic AI Mixer MCP Server",
    description="MCP server exposing Enhanced Cosmic AI Mixer 2025 functionality",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize the mixer
mixer = EnhancedCosmicAIMixer2025()

mcp_router = MCPRouter()

@mcp_router.tool(
    name="research_hvac_trends",
    description="Research HVAC industry trends using AI-powered analysis",
    input_schema={
        "type": "object",
        "properties": {
            "query": {"type": "string", "description": "Research topic"},
            "framework": {
                "type": "string", 
                "enum": ["native", "crewai", "pydantic_ai", "langchain"],
                "default": "native",
                "description": "AI framework to use"
            }
        },
        "required": ["query"]
    }
)
async def research_hvac_trends(query: str, framework: str = "native"):
    """MCP endpoint for HVAC research"""
    try:
        research_summary, trends_analysis, insights_chart = await mixer.research_and_inspire(query, framework)
        return {
            "research_summary": research_summary,
            "trends_analysis": trends_analysis,
            "insights_chart": insights_chart
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@mcp_router.tool(
    name="process_transcription",
    description="Process audio transcription with AI-powered analysis",
    input_schema={
        "type": "object",
        "properties": {
            "audio_path": {"type": "string", "description": "Path to audio file"},
            "framework": {
                "type": "string", 
                "enum": ["native", "crewai", "pydantic_ai", "langchain"],
                "default": "native",
                "description": "AI framework to use"
            },
            "analysis_type": {
                "type": "string", 
                "enum": ["comprehensive", "sentiment", "keywords", "summary"],
                "default": "comprehensive",
                "description": "Type of analysis to perform"
            }
        },
        "required": ["audio_path"]
    }
)
async def process_transcription(audio_path: str, framework: str = "native", analysis_type: str = "comprehensive"):
    """MCP endpoint for transcription processing"""
    try:
        # Read audio file
        with open(audio_path, "rb") as f:
            audio_data = f.read()
        
        transcription_text, ai_insights, analysis_chart = await mixer.process_transcription_with_ai(
            audio_data, framework, analysis_type
        )
        
        return {
            "transcription": transcription_text,
            "insights": ai_insights,
            "analysis_chart": analysis_chart
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@mcp_router.tool(
    name="analyze_data",
    description="Analyze HVAC data using multiple AI frameworks",
    input_schema={
        "type": "object",
        "properties": {
            "data": {"type": "string", "description": "Data to analyze"},
            "frameworks": {
                "type": "array",
                "items": {"type": "string", "enum": ["native", "crewai", "pydantic_ai", "langchain"]},
                "default": ["native", "crewai"],
                "description": "AI frameworks to use for comparison"
            }
        },
        "required": ["data"]
    }
)
async def analyze_data(data: str, frameworks: list = None):
    """MCP endpoint for multi-framework analysis"""
    try:
        if frameworks is None:
            frameworks = ["native", "crewai"]
        
        comparison_summary, framework_insights, comparison_chart = await mixer.analyze_data_with_multi_framework(data, frameworks)
        
        return {
            "comparison": comparison_summary,
            "insights": framework_insights,
            "chart": comparison_chart
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@mcp_router.resource(
    name="mixer_status",
    description="Get current status of the Cosmic AI Mixer"
)
async def get_mixer_status():
    """MCP resource for system status"""
    try:
        health_status, performance_stats, dashboard_chart = mixer.get_system_status()
        return {
            "health": health_status,
            "performance": performance_stats,
            "dashboard": dashboard_chart
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

app.include_router(mcp_router, prefix="/mcp")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8052)