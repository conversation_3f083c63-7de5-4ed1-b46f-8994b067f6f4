"""FastAPI server for Krabulon HVAC Data Enrichment System."""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from loguru import logger

import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents import AgentOrchestrator
from database import DatabaseManager
from config import settings


# Global instances
orchestrator: Optional[AgentOrchestrator] = None
db_manager: Optional[DatabaseManager] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global orchestrator, db_manager
    
    # Startup
    logger.info("Starting Krabulon API server")
    
    try:
        # Initialize orchestrator
        orchestrator = AgentOrchestrator()
        await orchestrator.initialize()
        
        # Initialize database manager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        logger.info("Krabulon API server started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start Krabulon API server: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Krabulon API server")
    
    try:
        if orchestrator:
            await orchestrator.cleanup()
        
        if db_manager:
            await db_manager.cleanup()
        
        logger.info("Krabulon API server shut down successfully")
        
    except Exception as e:
        logger.error(f"Error during server shutdown: {e}")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title=settings.app.api_title,
        description=settings.app.api_description,
        version=settings.app.version,
        docs_url=settings.app.docs_url,
        redoc_url=settings.app.redoc_url,
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    return app


# Create app instance
app = create_app()


# Pydantic models
class EnrichmentRequest(BaseModel):
    """Request model for enrichment workflow."""
    manufacturers: List[str] = Field(..., description="List of manufacturer names")
    priorities: Optional[Dict[str, int]] = Field(None, description="Priority mapping (1=highest, 5=lowest)")
    config: Optional[Dict[str, Any]] = Field(None, description="Additional configuration")


class EnrichmentResponse(BaseModel):
    """Response model for enrichment workflow."""
    workflow_id: str
    status: str
    message: str
    results: Optional[Dict[str, Any]] = None


class EquipmentSearchRequest(BaseModel):
    """Request model for equipment search."""
    search_term: str = Field(..., description="Search term")
    limit: int = Field(50, ge=1, le=200, description="Maximum results")
    database: str = Field("postgresql", description="Database to search")


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str
    timestamp: str
    components: Dict[str, Any]
    database_statistics: Optional[Dict[str, Any]] = None


# Dependency functions
async def get_orchestrator() -> AgentOrchestrator:
    """Get orchestrator instance."""
    if orchestrator is None:
        raise HTTPException(status_code=503, detail="Orchestrator not initialized")
    return orchestrator


async def get_database_manager() -> DatabaseManager:
    """Get database manager instance."""
    if db_manager is None:
        raise HTTPException(status_code=503, detail="Database manager not initialized")
    return db_manager


# API Routes
@app.get("/", response_model=Dict[str, str])
async def root():
    """Root endpoint with basic information."""
    return {
        "name": settings.app.name,
        "version": settings.app.version,
        "status": "operational",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check(orch: AgentOrchestrator = Depends(get_orchestrator)):
    """Comprehensive health check endpoint."""
    try:
        system_status = await orch.get_system_status()
        
        return HealthResponse(
            status=system_status.get("status", "unknown"),
            timestamp=system_status.get("timestamp", datetime.now().isoformat()),
            components=system_status.get("components", {}),
            database_statistics=system_status.get("database_statistics")
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


@app.post("/enrich", response_model=EnrichmentResponse)
async def start_enrichment(
    request: EnrichmentRequest,
    background_tasks: BackgroundTasks,
    orch: AgentOrchestrator = Depends(get_orchestrator)
):
    """Start HVAC data enrichment workflow."""
    try:
        logger.info(f"Starting enrichment for manufacturers: {request.manufacturers}")
        
        # Start workflow in background
        workflow_task = asyncio.create_task(
            orch.execute_enrichment_workflow(
                manufacturers=request.manufacturers,
                priorities=request.priorities,
                config=request.config
            )
        )
        
        # Get workflow ID from current state
        workflow_status = orch.get_workflow_status()
        workflow_id = workflow_status.get("id", f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
        return EnrichmentResponse(
            workflow_id=workflow_id,
            status="started",
            message=f"Enrichment workflow started for {len(request.manufacturers)} manufacturers",
            results=None
        )
        
    except Exception as e:
        logger.error(f"Failed to start enrichment: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start enrichment: {str(e)}")


@app.get("/workflow/status", response_model=Dict[str, Any])
async def get_workflow_status(orch: AgentOrchestrator = Depends(get_orchestrator)):
    """Get current workflow status."""
    try:
        status = orch.get_workflow_status()
        return status
        
    except Exception as e:
        logger.error(f"Failed to get workflow status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get workflow status: {str(e)}")


@app.post("/workflow/cancel", response_model=Dict[str, Any])
async def cancel_workflow(orch: AgentOrchestrator = Depends(get_orchestrator)):
    """Cancel current workflow."""
    try:
        result = await orch.cancel_workflow()
        return result
        
    except Exception as e:
        logger.error(f"Failed to cancel workflow: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel workflow: {str(e)}")


@app.get("/equipment", response_model=List[Dict[str, Any]])
async def list_equipment(
    manufacturer: Optional[str] = Query(None, description="Filter by manufacturer"),
    equipment_type: Optional[str] = Query(None, description="Filter by equipment type"),
    limit: int = Query(100, ge=1, le=500, description="Maximum results"),
    offset: int = Query(0, ge=0, description="Results offset"),
    database: str = Query("postgresql", description="Database to query"),
    db: DatabaseManager = Depends(get_database_manager)
):
    """List HVAC equipment from database."""
    try:
        equipment_list = await db.get_equipment(
            manufacturer=manufacturer,
            equipment_type=equipment_type,
            limit=limit,
            offset=offset,
            database=database
        )
        
        return equipment_list
        
    except Exception as e:
        logger.error(f"Failed to list equipment: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list equipment: {str(e)}")


@app.post("/equipment/search", response_model=List[Dict[str, Any]])
async def search_equipment(
    request: EquipmentSearchRequest,
    db: DatabaseManager = Depends(get_database_manager)
):
    """Search HVAC equipment in database."""
    try:
        results = await db.search_equipment(
            search_term=request.search_term,
            limit=request.limit,
            database=request.database
        )
        
        return results
        
    except Exception as e:
        logger.error(f"Failed to search equipment: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to search equipment: {str(e)}")


@app.get("/statistics", response_model=Dict[str, Any])
async def get_statistics(db: DatabaseManager = Depends(get_database_manager)):
    """Get database statistics."""
    try:
        stats = await db.get_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")


@app.get("/manufacturers", response_model=List[str])
async def list_manufacturers():
    """List configured manufacturers."""
    try:
        manufacturers = [mfg["name"] for mfg in settings.manufacturers]
        return manufacturers
        
    except Exception as e:
        logger.error(f"Failed to list manufacturers: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list manufacturers: {str(e)}")


@app.get("/databases", response_model=List[str])
async def list_databases(db: DatabaseManager = Depends(get_database_manager)):
    """List enabled database backends."""
    try:
        databases = db.get_enabled_databases()
        return databases
        
    except Exception as e:
        logger.error(f"Failed to list databases: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list databases: {str(e)}")


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Handle 404 errors."""
    return {"error": "Endpoint not found", "status_code": 404}


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {exc}")
    return {"error": "Internal server error", "status_code": 500}


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "server:app",
        host=settings.app.host,
        port=settings.app.port,
        reload=settings.app.debug,
        log_level="info"
    )
