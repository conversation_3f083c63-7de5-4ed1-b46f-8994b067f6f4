"""HVAC Equipment data models based on krabulon.md specification."""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class EquipmentType(str, Enum):
    """HVAC equipment types."""
    AIR_CONDITIONER = "air_conditioner"
    HEAT_PUMP = "heat_pump"
    VENTILATION = "ventilation"
    CHILLER = "chiller"
    BOILER = "boiler"
    FURNACE = "furnace"
    SPLIT_SYSTEM = "split_system"
    MULTI_SPLIT = "multi_split"
    VRF = "vrf"


class EnergyClass(str, Enum):
    """Energy efficiency classes."""
    A_PLUS_PLUS_PLUS = "A+++"
    A_PLUS_PLUS = "A++"
    A_PLUS = "A+"
    A = "A"
    B = "B"
    C = "C"
    D = "D"
    E = "E"
    F = "F"
    G = "G"


class BasicInfo(BaseModel):
    """Basic equipment information."""
    manufacturer: str = Field(..., description="Equipment manufacturer")
    model: str = Field(..., description="Equipment model")
    type: EquipmentType = Field(..., description="Equipment type")
    series: Optional[str] = Field(None, description="Product series")
    release_year: Optional[int] = Field(None, description="Release year")
    
    @validator('release_year')
    def validate_release_year(cls, v):
        if v is not None and (v < 1900 or v > datetime.now().year + 2):
            raise ValueError('Release year must be between 1900 and current year + 2')
        return v


class ValueWithUnit(BaseModel):
    """Value with unit representation."""
    value: float = Field(..., description="Numeric value")
    unit: str = Field(..., description="Unit of measurement")


class EnergyEfficiency(BaseModel):
    """Energy efficiency specifications."""
    cooling_eer: Optional[float] = Field(None, description="EER for cooling")
    heating_cop: Optional[float] = Field(None, description="COP for heating")
    seer: Optional[float] = Field(None, description="SEER rating")
    scop: Optional[float] = Field(None, description="SCOP rating")
    energy_class: Optional[EnergyClass] = Field(None, description="Energy class")


class Dimensions(BaseModel):
    """Physical dimensions."""
    width: Optional[float] = Field(None, description="Width")
    height: Optional[float] = Field(None, description="Height")
    depth: Optional[float] = Field(None, description="Depth")
    unit: str = Field("mm", description="Unit of measurement")


class Weight(BaseModel):
    """Weight specification."""
    value: Optional[float] = Field(None, description="Weight value")
    unit: str = Field("kg", description="Weight unit")


class Refrigerant(BaseModel):
    """Refrigerant specifications."""
    type: Optional[str] = Field(None, description="Refrigerant type (e.g., R32, R410A)")
    charge: Optional[float] = Field(None, description="Refrigerant charge amount")
    unit: str = Field("kg", description="Charge unit")


class Electrical(BaseModel):
    """Electrical specifications."""
    power_supply: Optional[str] = Field(None, description="Power supply (e.g., 230V, 50Hz)")
    cooling_power: Optional[float] = Field(None, description="Power consumption during cooling")
    heating_power: Optional[float] = Field(None, description="Power consumption during heating")
    unit: str = Field("W", description="Power unit")


class Airflow(BaseModel):
    """Airflow specifications."""
    value: Optional[float] = Field(None, description="Airflow rate")
    unit: str = Field("m³/h", description="Airflow unit")


class NoiseLevel(BaseModel):
    """Noise level specifications."""
    indoor: Optional[float] = Field(None, description="Indoor unit noise level")
    outdoor: Optional[float] = Field(None, description="Outdoor unit noise level")
    unit: str = Field("dB(A)", description="Noise unit")


class TemperatureRange(BaseModel):
    """Temperature range specification."""
    min: Optional[float] = Field(None, description="Minimum temperature")
    max: Optional[float] = Field(None, description="Maximum temperature")


class OperatingRange(BaseModel):
    """Operating temperature ranges."""
    cooling: Optional[TemperatureRange] = Field(None, description="Cooling mode range")
    heating: Optional[TemperatureRange] = Field(None, description="Heating mode range")
    unit: str = Field("°C", description="Temperature unit")


class TechnicalSpecs(BaseModel):
    """Technical specifications."""
    cooling_capacity: Optional[ValueWithUnit] = Field(None, description="Cooling capacity")
    heating_capacity: Optional[ValueWithUnit] = Field(None, description="Heating capacity")
    energy_efficiency: Optional[EnergyEfficiency] = Field(None, description="Energy efficiency")
    dimensions: Optional[Dimensions] = Field(None, description="Physical dimensions")
    weight: Optional[Weight] = Field(None, description="Weight")
    refrigerant: Optional[Refrigerant] = Field(None, description="Refrigerant specs")
    electrical: Optional[Electrical] = Field(None, description="Electrical specs")
    airflow: Optional[Airflow] = Field(None, description="Airflow specs")
    noise_level: Optional[NoiseLevel] = Field(None, description="Noise levels")
    operating_range: Optional[OperatingRange] = Field(None, description="Operating ranges")


class Documentation(BaseModel):
    """Documentation links."""
    manuals: List[str] = Field(default_factory=list, description="Manual URLs")
    datasheets: List[str] = Field(default_factory=list, description="Datasheet URLs")
    installation_guides: List[str] = Field(default_factory=list, description="Installation guide URLs")


class Metadata(BaseModel):
    """Extraction metadata."""
    source_url: str = Field(..., description="Source URL")
    extraction_date: datetime = Field(default_factory=datetime.now, description="Extraction date")
    last_updated: datetime = Field(default_factory=datetime.now, description="Last update date")
    confidence_score: float = Field(0.0, ge=0.0, le=1.0, description="Confidence score (0-1)")
    extractor_version: str = Field("1.0.0", description="Extractor version")
    
    @validator('confidence_score')
    def validate_confidence_score(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Confidence score must be between 0.0 and 1.0')
        return v


class HVACEquipment(BaseModel):
    """Complete HVAC equipment model."""
    id: Optional[str] = Field(None, description="Unique identifier")
    basic_info: BasicInfo = Field(..., description="Basic equipment information")
    technical_specs: Optional[TechnicalSpecs] = Field(None, description="Technical specifications")
    features: List[str] = Field(default_factory=list, description="Equipment features")
    certifications: List[str] = Field(default_factory=list, description="Certifications")
    documentation: Optional[Documentation] = Field(None, description="Documentation links")
    metadata: Metadata = Field(..., description="Extraction metadata")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage."""
        return self.dict(exclude_none=True)
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return self.json(exclude_none=True, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "HVACEquipment":
        """Create instance from dictionary."""
        return cls(**data)
    
    def update_metadata(self, **kwargs) -> None:
        """Update metadata fields."""
        for key, value in kwargs.items():
            if hasattr(self.metadata, key):
                setattr(self.metadata, key, value)
        self.metadata.last_updated = datetime.now()
    
    def get_unique_key(self) -> str:
        """Generate unique key for deduplication."""
        return f"{self.basic_info.manufacturer}_{self.basic_info.model}_{self.basic_info.type}"
    
    def is_complete(self) -> bool:
        """Check if equipment data is complete."""
        required_fields = [
            self.basic_info.manufacturer,
            self.basic_info.model,
            self.basic_info.type
        ]
        return all(field for field in required_fields)
    
    def get_missing_fields(self) -> List[str]:
        """Get list of missing critical fields."""
        missing = []
        
        if not self.basic_info.manufacturer:
            missing.append("manufacturer")
        if not self.basic_info.model:
            missing.append("model")
        if not self.basic_info.type:
            missing.append("type")
        
        if not self.technical_specs:
            missing.append("technical_specs")
        elif self.technical_specs:
            if not self.technical_specs.cooling_capacity:
                missing.append("cooling_capacity")
            if not self.technical_specs.energy_efficiency:
                missing.append("energy_efficiency")
        
        return missing
