# Krabulon - AI-Powered HVAC Equipment Data Enrichment System

Krabulon is a comprehensive system for automatically crawling, extracting, and enriching HVAC equipment databases using multi-agent AI systems and advanced web crawling technologies.

## Features

- **Multi-Agent AI System**: Coordinated agents for planning, crawling, extraction, validation, and integration
- **Advanced Web Crawling**: Powered by Crawl4AI with support for dynamic content and JavaScript
- **Multiple Database Support**: PostgreSQL, MongoDB, Neo4j, and GraphQL integrations
- **Comprehensive Data Validation**: AI-powered quality assurance and consistency checking
- **RESTful API**: Complete API for integration with existing systems
- **Real-time Monitoring**: Health checks, metrics, and workflow status tracking
- **Docker Support**: Containerized deployment with docker-compose
- **CLI Interface**: Command-line tools for manual operations

## Architecture

The system follows a multi-agent architecture with the following components:

1. **Planner Agent**: Identifies sources and plans crawling strategies
2. **Crawler Agent**: Executes web crawling operations using Crawl4AI
3. **Extractor Agent**: Processes and structures crawled HVAC data
4. **Validator Agent**: Ensures data quality and completeness
5. **Integrator Agent**: Saves validated data to multiple databases

## Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose (for containerized deployment)
- OpenAI API key
- PostgreSQL, MongoDB, Neo4j (or use Docker Compose)

### Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd krabulon
pip install -r requirements.txt
```

2. **Configure environment**:
```bash
export OPENAI_API_KEY="your-openai-api-key"
export SECRET_KEY="your-secret-key"
```

3. **Configure databases** (edit `config.yaml`):
```yaml
databases:
  postgresql:
    host: "localhost"
    port: 5432
    database: "hvac_crm"
    username: "postgres"
    password: "password"
```

### Docker Deployment

1. **Start all services**:
```bash
docker-compose up -d
```

2. **Check status**:
```bash
docker-compose ps
```

3. **View logs**:
```bash
docker-compose logs -f krabulon
```

### Manual Installation

1. **Install dependencies**:
```bash
pip install -r requirements.txt
playwright install chromium
```

2. **Initialize databases**:
```bash
# PostgreSQL setup
createdb hvac_crm
psql hvac_crm < init-scripts/init.sql
```

3. **Start the system**:
```bash
python -m krabulon.main serve
```

## Usage

### Command Line Interface

**Start enrichment workflow**:
```bash
python -m krabulon.main enrich --manufacturers LG Daikin Mitsubishi
```

**Check system status**:
```bash
python -m krabulon.main status
```

**List equipment**:
```bash
python -m krabulon.main list-equipment --manufacturer LG --limit 20
```

**Search equipment**:
```bash
python -m krabulon.main search "air conditioner R32"
```

**Start API server**:
```bash
python -m krabulon.main serve --host 0.0.0.0 --port 8000
```

### API Usage

**Start enrichment**:
```bash
curl -X POST "http://localhost:8000/enrich" \
  -H "Content-Type: application/json" \
  -d '{
    "manufacturers": ["LG", "Daikin"],
    "priorities": {"LG": 1, "Daikin": 2}
  }'
```

**Check workflow status**:
```bash
curl "http://localhost:8000/workflow/status"
```

**Search equipment**:
```bash
curl -X POST "http://localhost:8000/equipment/search" \
  -H "Content-Type: application/json" \
  -d '{
    "search_term": "heat pump",
    "limit": 50
  }'
```

**Get statistics**:
```bash
curl "http://localhost:8000/statistics"
```

### Python API

```python
import asyncio
from krabulon import AgentOrchestrator

async def main():
    orchestrator = AgentOrchestrator()
    await orchestrator.initialize()
    
    # Execute enrichment workflow
    results = await orchestrator.execute_enrichment_workflow(
        manufacturers=["LG", "Daikin", "Mitsubishi"],
        priorities={"LG": 1, "Daikin": 2, "Mitsubishi": 3}
    )
    
    print(f"Enrichment completed: {results['summary']}")
    
    await orchestrator.cleanup()

asyncio.run(main())
```

## Configuration

### Main Configuration (`config.yaml`)

```yaml
# Application settings
app:
  name: "Krabulon HVAC Data Enrichment System"
  debug: false
  host: "0.0.0.0"
  port: 8000

# AI Configuration
ai:
  openai:
    api_key: "${OPENAI_API_KEY}"
    model: "gpt-4-turbo-preview"
    temperature: 0.1

# Database configurations
databases:
  postgresql:
    host: "localhost"
    port: 5432
    database: "hvac_crm"
    username: "postgres"
    password: "password"

# Crawling settings
crawling:
  max_pages: 50
  delay: 1.0
  timeout: 30
  respect_robots_txt: true

# Manufacturers to crawl
manufacturers:
  - name: "LG"
    base_url: "https://www.lg.com"
    product_pages: ["/hvac/air-conditioners"]
  - name: "Daikin"
    base_url: "https://www.daikin.com"
    product_pages: ["/products/air-conditioners"]
```

### Environment Variables

```bash
# Required
OPENAI_API_KEY=your-openai-api-key
SECRET_KEY=your-secret-key

# Optional database overrides
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=hvac_crm
DATABASE_USER=postgres
DATABASE_PASSWORD=password

MONGODB_HOST=localhost
MONGODB_PORT=27017

NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

REDIS_HOST=localhost
REDIS_PORT=6379
```

## Data Model

### HVAC Equipment Structure

```json
{
  "id": "unique-equipment-id",
  "basic_info": {
    "manufacturer": "LG",
    "model": "S12ET",
    "type": "air_conditioner",
    "series": "Dual Cool",
    "release_year": 2023
  },
  "technical_specs": {
    "cooling_capacity": {"value": 3.5, "unit": "kW"},
    "heating_capacity": {"value": 4.0, "unit": "kW"},
    "energy_efficiency": {
      "cooling_eer": 3.2,
      "heating_cop": 4.1,
      "seer": 16.0,
      "energy_class": "A++"
    },
    "dimensions": {"width": 800, "height": 285, "depth": 300, "unit": "mm"},
    "weight": {"value": 9.5, "unit": "kg"},
    "refrigerant": {"type": "R32", "charge": 0.65, "unit": "kg"}
  },
  "features": ["WiFi", "Inverter", "Dual Cool Technology"],
  "certifications": ["Energy Star", "CE"],
  "metadata": {
    "source_url": "https://example.com/product",
    "extraction_date": "2024-01-15T10:30:00Z",
    "confidence_score": 0.95
  }
}
```

## Monitoring and Health Checks

### Health Check Endpoints

- **System Health**: `GET /health`
- **Workflow Status**: `GET /workflow/status`
- **Database Statistics**: `GET /statistics`

### Monitoring Stack

When using Docker Compose, the following monitoring tools are available:

- **Prometheus**: Metrics collection (http://localhost:9090)
- **Grafana**: Dashboards and visualization (http://localhost:3000)
- **Application Logs**: Structured logging with Loguru

### Key Metrics

- Crawling success rate
- Data extraction accuracy
- Validation pass rate
- Database integration status
- API response times
- System resource usage

## Development

### Project Structure

```
krabulon/
├── agents/              # AI agents implementation
│   ├── base_agent.py   # Base agent class
│   ├── planner.py      # Planning agent
│   ├── crawler.py      # Crawling agent
│   ├── extractor.py    # Data extraction agent
│   ├── validator.py    # Data validation agent
│   ├── integrator.py   # Database integration agent
│   └── orchestrator.py # Workflow orchestration
├── crawlers/           # Web crawling components
│   ├── crawl4ai_wrapper.py
│   └── manager.py
├── database/           # Database integrations
│   ├── postgresql.py
│   ├── mongodb.py
│   ├── neo4j.py
│   └── manager.py
├── models/             # Data models
│   └── hvac_equipment.py
├── api/                # REST API
│   └── server.py
├── config/             # Configuration management
│   └── settings.py
└── main.py            # CLI entry point
```

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run tests
pytest tests/ -v

# Run with coverage
pytest tests/ --cov=krabulon --cov-report=html
```

### Code Quality

```bash
# Format code
black krabulon/
isort krabulon/

# Lint code
flake8 krabulon/
mypy krabulon/
```

## Troubleshooting

### Common Issues

1. **OpenAI API Key Error**:
   - Ensure `OPENAI_API_KEY` environment variable is set
   - Check API key validity and quota

2. **Database Connection Issues**:
   - Verify database credentials in `config.yaml`
   - Ensure databases are running and accessible
   - Check firewall and network settings

3. **Crawling Failures**:
   - Check internet connectivity
   - Verify target websites are accessible
   - Review robots.txt compliance settings

4. **Memory Issues**:
   - Reduce batch sizes in configuration
   - Increase Docker memory limits
   - Monitor system resources

### Logs and Debugging

```bash
# View application logs
tail -f logs/krabulon.log

# Docker logs
docker-compose logs -f krabulon

# Enable debug mode
export DEBUG=true
python -m krabulon.main serve --reload
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the troubleshooting section

## Changelog

### Version 1.0.0
- Initial release
- Multi-agent AI system
- Crawl4AI integration
- PostgreSQL, MongoDB, Neo4j support
- REST API
- Docker deployment
- CLI interface
