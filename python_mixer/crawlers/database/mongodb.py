
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""MongoDB database handler for HVAC equipment data."""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import DuplicateKeyError, ConnectionFailure
from loguru import logger

from config import settings


class MongoDBHandler:
    """MongoDB handler for storing and retrieving HVAC equipment data."""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database = None
        self.collection = None
        self.connected = False
    
    async def initialize(self):
        """Initialize MongoDB connection."""
        try:
            # Create MongoDB client with optional authentication
            if settings.database.mongodb_username and settings.database.mongodb_password:
                mongodb_url = f"mongodb://{settings.database.mongodb_username}:{settings.database.mongodb_password}@{settings.database.mongodb_host}:{settings.database.mongodb_port}/{settings.database.mongodb_database}?authSource=admin"
            else:
                mongodb_url = f"mongodb://{settings.database.mongodb_host}:{settings.database.mongodb_port}/{settings.database.mongodb_database}"

            self.client = AsyncIOMotorClient(
                mongodb_url,
                serverSelectionTimeoutMS=5000
            )
            
            # Get database and collection
            self.database = self.client[settings.database.mongodb_database]
            self.collection = self.database.hvac_equipment
            
            # Test connection
            await self.client.admin.command('ping')
            
            # Create indexes
            await self._create_indexes()
            
            self.connected = True
            logger.info("MongoDB connection established successfully")
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self.connected = False
            raise
        except Exception as e:
            logger.error(f"MongoDB initialization error: {e}")
            self.connected = False
            raise
    
    async def cleanup(self):
        """Cleanup MongoDB connection."""
        if self.client:
            self.client.close()
            self.connected = False
            logger.info("MongoDB connection closed")
    
    async def _create_indexes(self):
        """Create necessary indexes for efficient querying."""
        try:
            # Compound index for manufacturer and model
            await self.collection.create_index([
                ("basic_info.manufacturer", 1),
                ("basic_info.model", 1)
            ], unique=True)
            
            # Text index for search
            await self.collection.create_index([
                ("basic_info.manufacturer", "text"),
                ("basic_info.model", "text"),
                ("basic_info.type", "text"),
                ("features", "text")
            ])
            
            # Index for metadata
            await self.collection.create_index("metadata.extraction_date")
            await self.collection.create_index("metadata.confidence_score")
            
            logger.info("MongoDB indexes created successfully")
            
        except Exception as e:
            logger.warning(f"Failed to create MongoDB indexes: {e}")
    
    async def save_equipment(self, equipment_data: Dict[str, Any]) -> str:
        """Save equipment data to MongoDB."""
        try:
            # Add timestamp
            equipment_data["metadata"]["saved_at"] = datetime.now().isoformat()
            
            # Insert document
            result = await self.collection.insert_one(equipment_data)
            
            logger.info(f"Equipment saved to MongoDB: {equipment_data['basic_info']['manufacturer']} {equipment_data['basic_info']['model']}")
            return str(result.inserted_id)
            
        except DuplicateKeyError:
            # Update existing document
            filter_query = {
                "basic_info.manufacturer": equipment_data["basic_info"]["manufacturer"],
                "basic_info.model": equipment_data["basic_info"]["model"]
            }
            
            equipment_data["metadata"]["updated_at"] = datetime.now().isoformat()
            
            result = await self.collection.replace_one(filter_query, equipment_data)
            
            if result.matched_count > 0:
                logger.info(f"Equipment updated in MongoDB: {equipment_data['basic_info']['manufacturer']} {equipment_data['basic_info']['model']}")
                return "updated"
            else:
                logger.warning(f"Failed to update equipment in MongoDB")
                return "failed"
                
        except Exception as e:
            logger.error(f"Failed to save equipment to MongoDB: {e}")
            raise
    
    async def get_equipment(
        self,
        manufacturer: Optional[str] = None,
        equipment_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get equipment data from MongoDB."""
        try:
            query = {}
            
            if manufacturer:
                query["basic_info.manufacturer"] = {"$regex": manufacturer, "$options": "i"}
            
            if equipment_type:
                query["basic_info.type"] = {"$regex": equipment_type, "$options": "i"}
            
            cursor = self.collection.find(query).limit(limit)
            equipment_list = await cursor.to_list(length=limit)
            
            # Convert ObjectId to string
            for equipment in equipment_list:
                equipment["_id"] = str(equipment["_id"])
            
            return equipment_list
            
        except Exception as e:
            logger.error(f"Failed to get equipment from MongoDB: {e}")
            raise
    
    async def search_equipment(self, search_term: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search equipment using text search."""
        try:
            cursor = self.collection.find(
                {"$text": {"$search": search_term}},
                {"score": {"$meta": "textScore"}}
            ).sort([("score", {"$meta": "textScore"})]).limit(limit)
            
            equipment_list = await cursor.to_list(length=limit)
            
            # Convert ObjectId to string
            for equipment in equipment_list:
                equipment["_id"] = str(equipment["_id"])
            
            return equipment_list
            
        except Exception as e:
            logger.error(f"Failed to search equipment in MongoDB: {e}")
            raise
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            total_count = await self.collection.count_documents({})
            
            # Aggregate by manufacturer
            manufacturer_pipeline = [
                {"$group": {"_id": "$basic_info.manufacturer", "count": {"$sum": 1}}},
                {"$sort": {"count": -1}}
            ]
            manufacturer_stats = await self.collection.aggregate(manufacturer_pipeline).to_list(None)
            
            # Average confidence score
            confidence_pipeline = [
                {"$group": {"_id": None, "avg_confidence": {"$avg": "$metadata.confidence_score"}}}
            ]
            confidence_result = await self.collection.aggregate(confidence_pipeline).to_list(None)
            avg_confidence = confidence_result[0]["avg_confidence"] if confidence_result else 0
            
            return {
                "total_equipment": total_count,
                "manufacturers": manufacturer_stats,
                "average_confidence": avg_confidence
            }
            
        except Exception as e:
            logger.error(f"Failed to get MongoDB statistics: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on MongoDB connection."""
        try:
            if not self.connected or not self.client:
                return {"healthy": False, "error": "Not connected"}
            
            # Test connection
            await self.client.admin.command('ping')
            
            # Get basic stats
            stats = await self.get_statistics()
            
            return {
                "healthy": True,
                "total_equipment": stats["total_equipment"],
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
