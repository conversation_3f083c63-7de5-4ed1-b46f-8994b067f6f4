
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Redis cache handler for HVAC equipment data."""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import redis.asyncio as redis
from loguru import logger

from config import settings


class RedisHandler:
    """Redis handler for caching HVAC equipment data and workflow state."""
    
    def __init__(self):
        self.client: Optional[redis.Redis] = None
        self.connected = False
        self.default_ttl = 3600  # 1 hour default TTL
    
    async def initialize(self):
        """Initialize Redis connection."""
        try:
            self.client = redis.Redis(
                host=settings.database.redis_host,
                port=settings.database.redis_port,
                db=settings.database.redis_database,
                password=settings.database.redis_password if settings.database.redis_password else None,
                decode_responses=True
            )
            
            # Test connection
            await self.client.ping()
            
            self.connected = True
            logger.info("Redis connection established successfully")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.connected = False
            raise
    
    async def cleanup(self):
        """Cleanup Redis connection."""
        if self.client:
            await self.client.close()
            self.connected = False
            logger.info("Redis connection closed")
    
    async def cache_equipment(self, equipment_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Cache equipment data."""
        try:
            key = f"equipment:{equipment_data['basic_info']['manufacturer']}:{equipment_data['basic_info']['model']}"
            value = json.dumps(equipment_data, default=str)
            
            await self.client.setex(
                key, 
                ttl or self.default_ttl, 
                value
            )
            
            logger.debug(f"Equipment cached: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache equipment: {e}")
            return False
    
    async def get_cached_equipment(self, manufacturer: str, model: str) -> Optional[Dict[str, Any]]:
        """Get cached equipment data."""
        try:
            key = f"equipment:{manufacturer}:{model}"
            value = await self.client.get(key)
            
            if value:
                return json.loads(value)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached equipment: {e}")
            return None
    
    async def cache_search_results(self, search_term: str, results: List[Dict[str, Any]], ttl: Optional[int] = None) -> bool:
        """Cache search results."""
        try:
            key = f"search:{search_term}"
            value = json.dumps(results, default=str)
            
            await self.client.setex(
                key,
                ttl or (self.default_ttl // 2),  # Shorter TTL for search results
                value
            )
            
            logger.debug(f"Search results cached: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache search results: {e}")
            return False
    
    async def get_cached_search_results(self, search_term: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached search results."""
        try:
            key = f"search:{search_term}"
            value = await self.client.get(key)
            
            if value:
                return json.loads(value)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached search results: {e}")
            return None
    
    async def cache_workflow_state(self, workflow_id: str, state: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Cache workflow state."""
        try:
            key = f"workflow:{workflow_id}"
            value = json.dumps(state, default=str)
            
            await self.client.setex(
                key,
                ttl or (self.default_ttl * 24),  # Longer TTL for workflow state
                value
            )
            
            logger.debug(f"Workflow state cached: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache workflow state: {e}")
            return False
    
    async def get_workflow_state(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get cached workflow state."""
        try:
            key = f"workflow:{workflow_id}"
            value = await self.client.get(key)
            
            if value:
                return json.loads(value)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get workflow state: {e}")
            return None
    
    async def cache_statistics(self, stats: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Cache database statistics."""
        try:
            key = "statistics:global"
            value = json.dumps(stats, default=str)
            
            await self.client.setex(
                key,
                ttl or 300,  # 5 minutes TTL for statistics
                value
            )
            
            logger.debug("Statistics cached")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cache statistics: {e}")
            return False
    
    async def get_cached_statistics(self) -> Optional[Dict[str, Any]]:
        """Get cached statistics."""
        try:
            key = "statistics:global"
            value = await self.client.get(key)
            
            if value:
                return json.loads(value)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get cached statistics: {e}")
            return None
    
    async def invalidate_cache(self, pattern: str) -> int:
        """Invalidate cache entries matching pattern."""
        try:
            keys = await self.client.keys(pattern)
            if keys:
                deleted = await self.client.delete(*keys)
                logger.info(f"Invalidated {deleted} cache entries matching pattern: {pattern}")
                return deleted
            
            return 0
            
        except Exception as e:
            logger.error(f"Failed to invalidate cache: {e}")
            return 0
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """Get cache information and statistics."""
        try:
            info = await self.client.info()
            
            # Get key counts by pattern
            equipment_keys = len(await self.client.keys("equipment:*"))
            search_keys = len(await self.client.keys("search:*"))
            workflow_keys = len(await self.client.keys("workflow:*"))
            
            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "total_keys": info.get("db0", {}).get("keys", 0),
                "equipment_keys": equipment_keys,
                "search_keys": search_keys,
                "workflow_keys": workflow_keys,
                "hits": info.get("keyspace_hits", 0),
                "misses": info.get("keyspace_misses", 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to get cache info: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Redis connection."""
        try:
            if not self.connected or not self.client:
                return {"healthy": False, "error": "Not connected"}
            
            # Test connection
            await self.client.ping()
            
            # Get basic info
            cache_info = await self.get_cache_info()
            
            return {
                "healthy": True,
                "total_keys": cache_info.get("total_keys", 0),
                "used_memory": cache_info.get("used_memory", "0B"),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
