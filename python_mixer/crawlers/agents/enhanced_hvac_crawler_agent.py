
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Enhanced HVAC Equipment Crawler Agent with 2024 features."""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
from loguru import logger

from ..crawlers.manager import CrawlerManager
from ..crawlers.crawl4ai_wrapper import Crawl4AIWrapper
from config import settings


class EnhancedHVACCrawlerAgent:
    """Advanced HVAC equipment crawler agent with modern extraction capabilities."""
    
    def __init__(self):
        self.crawler_manager = CrawlerManager()
        self.results_storage = Path("data/hvac_crawl_results")
        self.results_storage.mkdir(parents=True, exist_ok=True)
        
        # HVAC manufacturer URLs for comprehensive crawling
        self.manufacturer_urls = {
            "lg": [
                "https://lghvac.com/residential-light-commercial/",
                "https://lghvac.com/commercial/",
                "https://www.lg.com/global/business/hvac/technical-data"
            ],
            "daikin": [
                "https://www.daikin.com/products/ac",
                "https://www.northamerica-daikin.com/",
                "https://www.daikinac.com/"
            ],
            "carrier": [
                "https://www.carrier.com/residential/en/us/products/",
                "https://www.carrier.com/commercial/en/us/products/"
            ],
            "trane": [
                "https://www.trane.com/residential/en/products/",
                "https://www.trane.com/commercial/en/products/"
            ]
        }
    
    async def crawl_manufacturer_equipment(
        self,
        manufacturer: str,
        extraction_mode: str = "hybrid",
        max_pages_per_url: int = 10,
        save_images: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Crawl equipment data from a specific manufacturer."""
        
        if manufacturer.lower() not in self.manufacturer_urls:
            raise ValueError(f"Manufacturer {manufacturer} not supported. Available: {list(self.manufacturer_urls.keys())}")
        
        logger.info(f"Starting enhanced crawl for {manufacturer} equipment")
        start_time = datetime.now()
        
        all_results = []
        manufacturer_urls = self.manufacturer_urls[manufacturer.lower()]
        
        async with Crawl4AIWrapper(enable_stealth=True, enable_image_extraction=save_images) as crawler:
            for url in manufacturer_urls:
                try:
                    logger.info(f"Crawling {manufacturer} URL: {url}")
                    
                    result = await crawler.crawl_hvac_equipment(
                        url=url,
                        extraction_mode=extraction_mode,
                        save_images=save_images,
                        extract_tables=True,
                        **kwargs
                    )
                    
                    if result["success"]:
                        all_results.append(result)
                        logger.info(f"Successfully crawled {url}: {len(result['equipment_data'])} equipment items found")
                    else:
                        logger.warning(f"Failed to crawl {url}: {result.get('error', 'Unknown error')}")
                
                except Exception as e:
                    logger.error(f"Error crawling {url}: {e}")
                    continue
                
                # Add delay between requests
                await asyncio.sleep(2)
        
        # Aggregate results
        aggregated_result = self._aggregate_manufacturer_results(
            manufacturer, all_results, start_time
        )
        
        # Save results
        await self._save_crawl_results(manufacturer, aggregated_result)
        
        logger.info(f"Completed {manufacturer} crawl: {aggregated_result['total_equipment']} items found")
        return aggregated_result
    
    async def crawl_all_manufacturers(
        self,
        extraction_mode: str = "hybrid",
        max_concurrent: int = 2,
        **kwargs
    ) -> Dict[str, Any]:
        """Crawl equipment data from all supported manufacturers."""
        
        logger.info("Starting comprehensive HVAC equipment crawl for all manufacturers")
        start_time = datetime.now()
        
        # Create semaphore to limit concurrent crawls
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_manufacturer_with_semaphore(manufacturer: str):
            async with semaphore:
                return await self.crawl_manufacturer_equipment(
                    manufacturer=manufacturer,
                    extraction_mode=extraction_mode,
                    **kwargs
                )
        
        # Execute crawls for all manufacturers
        tasks = [
            crawl_manufacturer_with_semaphore(manufacturer)
            for manufacturer in self.manufacturer_urls.keys()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        successful_results = {}
        failed_manufacturers = []
        
        for i, result in enumerate(results):
            manufacturer = list(self.manufacturer_urls.keys())[i]
            if isinstance(result, Exception):
                logger.error(f"Failed to crawl {manufacturer}: {result}")
                failed_manufacturers.append(manufacturer)
            else:
                successful_results[manufacturer] = result
        
        # Create comprehensive summary
        summary = self._create_comprehensive_summary(
            successful_results, failed_manufacturers, start_time
        )
        
        # Save comprehensive results
        await self._save_comprehensive_results(summary)
        
        logger.info(f"Completed comprehensive crawl: {summary['total_equipment_across_all']} items from {len(successful_results)} manufacturers")
        return summary
    
    async def search_specific_equipment(
        self,
        equipment_type: str,
        capacity_range: Optional[tuple] = None,
        efficiency_min: Optional[float] = None,
        manufacturers: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """Search for specific equipment based on criteria."""
        
        logger.info(f"Searching for {equipment_type} equipment with specific criteria")
        
        # Load existing crawl results
        all_equipment = await self._load_all_equipment_data()
        
        # Filter based on criteria
        filtered_equipment = []
        
        for item in all_equipment:
            # Check equipment type
            if equipment_type.lower() not in item.get('equipment_type', '').lower():
                continue
            
            # Check manufacturer
            if manufacturers and item.get('manufacturer', '').lower() not in [m.lower() for m in manufacturers]:
                continue
            
            # Check capacity range
            if capacity_range:
                capacity_str = item.get('capacity_btu', '')
                try:
                    capacity = float(''.join(filter(str.isdigit, capacity_str)))
                    if not (capacity_range[0] <= capacity <= capacity_range[1]):
                        continue
                except (ValueError, TypeError):
                    continue
            
            # Check efficiency
            if efficiency_min:
                efficiency_str = item.get('energy_efficiency', '')
                try:
                    efficiency = float(''.join(filter(str.isdigit, efficiency_str.split()[0])))
                    if efficiency < efficiency_min:
                        continue
                except (ValueError, TypeError, IndexError):
                    continue
            
            filtered_equipment.append(item)
        
        logger.info(f"Found {len(filtered_equipment)} equipment items matching criteria")
        return filtered_equipment
    
    def _aggregate_manufacturer_results(
        self,
        manufacturer: str,
        results: List[Dict[str, Any]],
        start_time: datetime
    ) -> Dict[str, Any]:
        """Aggregate results from multiple URLs for a manufacturer."""
        
        all_equipment = []
        all_images = []
        all_tables = []
        total_pages = len(results)
        successful_pages = len([r for r in results if r["success"]])
        
        for result in results:
            if result["success"]:
                all_equipment.extend(result.get("equipment_data", []))
                all_images.extend(result.get("images", []))
                all_tables.extend(result.get("tables", []))
        
        return {
            "manufacturer": manufacturer,
            "total_equipment": len(all_equipment),
            "equipment_data": all_equipment,
            "total_images": len(all_images),
            "images": all_images,
            "total_tables": len(all_tables),
            "tables": all_tables,
            "crawl_statistics": {
                "total_pages_attempted": total_pages,
                "successful_pages": successful_pages,
                "success_rate": successful_pages / total_pages if total_pages > 0 else 0,
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
        }
    
    def _create_comprehensive_summary(
        self,
        manufacturer_results: Dict[str, Dict[str, Any]],
        failed_manufacturers: List[str],
        start_time: datetime
    ) -> Dict[str, Any]:
        """Create comprehensive summary of all manufacturer crawls."""
        
        total_equipment = sum(result["total_equipment"] for result in manufacturer_results.values())
        total_images = sum(result["total_images"] for result in manufacturer_results.values())
        total_tables = sum(result["total_tables"] for result in manufacturer_results.values())
        
        return {
            "total_equipment_across_all": total_equipment,
            "total_images_across_all": total_images,
            "total_tables_across_all": total_tables,
            "successful_manufacturers": list(manufacturer_results.keys()),
            "failed_manufacturers": failed_manufacturers,
            "manufacturer_results": manufacturer_results,
            "summary_statistics": {
                "manufacturers_attempted": len(manufacturer_results) + len(failed_manufacturers),
                "manufacturers_successful": len(manufacturer_results),
                "overall_success_rate": len(manufacturer_results) / (len(manufacturer_results) + len(failed_manufacturers)),
                "start_time": start_time.isoformat(),
                "end_time": datetime.now().isoformat(),
                "total_duration_seconds": (datetime.now() - start_time).total_seconds()
            }
        }
    
    async def _save_crawl_results(self, manufacturer: str, results: Dict[str, Any]):
        """Save crawl results to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{manufacturer}_equipment_{timestamp}.json"
        filepath = self.results_storage / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {manufacturer} results to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save {manufacturer} results: {e}")
    
    async def _save_comprehensive_results(self, summary: Dict[str, Any]):
        """Save comprehensive crawl results."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"comprehensive_hvac_crawl_{timestamp}.json"
        filepath = self.results_storage / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved comprehensive results to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save comprehensive results: {e}")
    
    async def _load_all_equipment_data(self) -> List[Dict[str, Any]]:
        """Load all equipment data from saved results."""
        all_equipment = []
        
        try:
            for filepath in self.results_storage.glob("*.json"):
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if "equipment_data" in data:
                        all_equipment.extend(data["equipment_data"])
                    elif "manufacturer_results" in data:
                        for manufacturer_data in data["manufacturer_results"].values():
                            all_equipment.extend(manufacturer_data.get("equipment_data", []))
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
        
        return all_equipment
