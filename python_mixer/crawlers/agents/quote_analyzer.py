"""Quote Analyzer Agent for analyzing customer communication and requirements."""

import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .base_agent import BaseAgent


class QuoteAnalyzerAgent(BaseAgent):
    """Agent responsible for analyzing customer communication to extract quote requirements."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="QuoteAnalyzer",
            role="Customer Communication Analysis Specialist",
            goal="Analyze customer communication history to extract HVAC equipment requirements and preferences",
            backstory=(
                "You are an expert in customer communication analysis with deep understanding "
                "of HVAC industry needs. You can read between the lines of customer emails, "
                "phone transcripts, and service requests to identify their true equipment needs, "
                "budget constraints, and preferences. Your analysis forms the foundation for "
                "creating personalized and accurate equipment recommendations."
            ),
            **kwargs
        )
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute customer communication analysis for quote generation.
        
        Args:
            input_data: Customer communication data and analysis parameters
            **kwargs: Additional analysis parameters
            
        Returns:
            Comprehensive customer analysis results
        """
        try:
            logger.info("Starting customer communication analysis")
            
            # Validate input
            if not self.validate_input(input_data, ["customer_id", "communication_history"]):
                raise ValueError("Invalid input data for quote analysis")
            
            customer_id = input_data["customer_id"]
            communication_history = input_data["communication_history"]
            analysis_config = input_data.get("config", {})
            
            # Perform comprehensive customer analysis
            customer_analysis = await self._analyze_customer_communication(
                customer_id,
                communication_history,
                analysis_config
            )
            
            # Extract equipment requirements
            equipment_requirements = await self._extract_equipment_requirements(
                customer_analysis,
                communication_history
            )
            
            # Analyze customer preferences and constraints
            preferences_analysis = await self._analyze_customer_preferences(
                customer_analysis,
                communication_history
            )
            
            # Calculate customer value and business potential
            business_analysis = await self._analyze_business_potential(
                customer_analysis,
                communication_history
            )
            
            # Compile analysis results
            analysis_results = {
                "analysis_summary": {
                    "customer_id": customer_id,
                    "analysis_date": datetime.now().isoformat(),
                    "communication_items_analyzed": len(communication_history),
                    "confidence_score": customer_analysis.get("confidence_score", 0.0),
                    "analyzer_version": "1.0.0"
                },
                "customer_analysis": customer_analysis,
                "equipment_requirements": equipment_requirements,
                "preferences_analysis": preferences_analysis,
                "business_analysis": business_analysis,
                "quote_recommendations": await self._generate_quote_recommendations(
                    equipment_requirements, preferences_analysis, business_analysis
                )
            }
            
            logger.info(f"Customer analysis completed for customer {customer_id}")
            return analysis_results
            
        except Exception as e:
            logger.error(f"Customer analysis failed: {e}")
            raise
    
    async def _analyze_customer_communication(
        self,
        customer_id: str,
        communication_history: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze customer communication patterns and extract insights."""
        
        # Prepare communication content for analysis
        communication_text = self._prepare_communication_text(communication_history)
        
        analysis_prompt = f"""
        Analyze the following customer communication history for HVAC equipment quote generation.
        
        Customer ID: {customer_id}
        Communication History ({len(communication_history)} items):
        {communication_text}
        
        Provide comprehensive analysis in JSON format:
        {{
            "customer_profile": {{
                "communication_style": "formal|casual|technical|urgent",
                "technical_knowledge": "high|medium|low",
                "decision_making_style": "analytical|quick|collaborative|price_sensitive",
                "urgency_level": "immediate|soon|planning|future",
                "budget_indicators": "premium|standard|budget|unspecified"
            }},
            "communication_patterns": {{
                "primary_concerns": ["list", "of", "main", "concerns"],
                "mentioned_issues": ["current", "problems", "or", "needs"],
                "timeline_indicators": ["when", "they", "need", "solution"],
                "location_details": ["property", "type", "and", "details"],
                "previous_experience": ["past", "hvac", "experience"]
            }},
            "sentiment_analysis": {{
                "overall_sentiment": "positive|neutral|negative",
                "satisfaction_level": "high|medium|low",
                "trust_indicators": ["signs", "of", "trust", "or", "skepticism"],
                "pain_points": ["identified", "frustrations"]
            }},
            "technical_requirements": {{
                "space_details": {{
                    "property_type": "residential|commercial|industrial",
                    "space_size": "estimated square meters or rooms",
                    "building_age": "new|modern|old",
                    "insulation_quality": "good|average|poor|unknown"
                }},
                "current_system": {{
                    "existing_equipment": "description of current HVAC",
                    "system_age": "estimated age",
                    "performance_issues": ["current", "problems"],
                    "replacement_reason": "why replacing or upgrading"
                }},
                "specific_needs": {{
                    "cooling_requirements": "high|medium|low|none",
                    "heating_requirements": "high|medium|low|none",
                    "energy_efficiency_priority": "high|medium|low",
                    "noise_sensitivity": "high|medium|low",
                    "smart_features_interest": "high|medium|low|none"
                }}
            }},
            "confidence_score": 0.0-1.0,
            "analysis_notes": "Additional insights and observations"
        }}
        
        Focus on extracting actionable insights for HVAC equipment recommendation.
        """
        
        try:
            response = await self.run_task(analysis_prompt)
            analysis_data = json.loads(response)
            
            # Add metadata
            analysis_data["analysis_metadata"] = {
                "analyzed_at": datetime.now().isoformat(),
                "communication_count": len(communication_history),
                "analysis_method": "AI_comprehensive"
            }
            
            return analysis_data
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to analyze customer communication: {e}")
            
            # Fallback to basic analysis
            return await self._fallback_communication_analysis(
                customer_id, communication_history
            )
    
    def _prepare_communication_text(self, communication_history: List[Dict[str, Any]]) -> str:
        """Prepare communication history for analysis."""
        
        text_parts = []
        
        for i, comm in enumerate(communication_history):
            comm_type = comm.get("type", "unknown")
            date = comm.get("date", "unknown")
            content = comm.get("content", "")
            
            # Truncate very long content
            if len(content) > 1000:
                content = content[:1000] + "..."
            
            text_parts.append(f"[{i+1}] {comm_type.upper()} ({date}):\n{content}\n")
        
        return "\n".join(text_parts)
    
    async def _extract_equipment_requirements(
        self,
        customer_analysis: Dict[str, Any],
        communication_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extract specific equipment requirements from analysis."""
        
        technical_reqs = customer_analysis.get("technical_requirements", {})
        space_details = technical_reqs.get("space_details", {})
        current_system = technical_reqs.get("current_system", {})
        specific_needs = technical_reqs.get("specific_needs", {})
        
        requirements_prompt = f"""
        Based on the customer analysis, extract specific HVAC equipment requirements.
        
        Customer Analysis Summary:
        - Property Type: {space_details.get("property_type", "unknown")}
        - Space Size: {space_details.get("space_size", "unknown")}
        - Current System: {current_system.get("existing_equipment", "none")}
        - Cooling Needs: {specific_needs.get("cooling_requirements", "unknown")}
        - Heating Needs: {specific_needs.get("heating_requirements", "unknown")}
        - Energy Efficiency Priority: {specific_needs.get("energy_efficiency_priority", "unknown")}
        
        Generate specific equipment requirements in JSON format:
        {{
            "system_type": "split|multi_split|vrf|ducted|window",
            "capacity_requirements": {{
                "cooling_capacity_kw": number or null,
                "heating_capacity_kw": number or null,
                "estimated_room_count": number or null,
                "total_area_m2": number or null
            }},
            "performance_requirements": {{
                "energy_efficiency_class": "A+++|A++|A+|A|B|C|any",
                "noise_level_max_db": number or null,
                "operating_temperature_range": {{
                    "min_temp": number or null,
                    "max_temp": number or null
                }}
            }},
            "feature_requirements": {{
                "wifi_connectivity": true|false|null,
                "smart_controls": true|false|null,
                "air_purification": true|false|null,
                "inverter_technology": true|false|null,
                "dual_zone_control": true|false|null
            }},
            "installation_requirements": {{
                "indoor_unit_locations": ["list", "of", "preferred", "locations"],
                "outdoor_unit_constraints": ["space", "limitations", "or", "preferences"],
                "electrical_requirements": "standard|upgraded|unknown",
                "piping_requirements": "standard|extended|complex"
            }},
            "budget_constraints": {{
                "budget_range": "premium|mid_range|budget|unspecified",
                "price_sensitivity": "high|medium|low",
                "financing_interest": true|false|null
            }},
            "timeline_requirements": {{
                "urgency": "immediate|within_week|within_month|flexible",
                "preferred_installation_time": "morning|afternoon|weekend|flexible",
                "seasonal_preference": "before_summer|before_winter|any"
            }}
        }}
        """
        
        try:
            response = await self.run_task(requirements_prompt)
            requirements = json.loads(response)
            
            # Add confidence scoring
            requirements["requirements_confidence"] = self._calculate_requirements_confidence(
                requirements, customer_analysis
            )
            
            return requirements
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to extract equipment requirements: {e}")
            
            # Return basic requirements
            return {
                "system_type": "split",
                "capacity_requirements": {},
                "performance_requirements": {},
                "feature_requirements": {},
                "installation_requirements": {},
                "budget_constraints": {"budget_range": "unspecified"},
                "timeline_requirements": {"urgency": "flexible"},
                "requirements_confidence": 0.3
            }
    
    async def _analyze_customer_preferences(
        self,
        customer_analysis: Dict[str, Any],
        communication_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze customer preferences and decision-making factors."""
        
        customer_profile = customer_analysis.get("customer_profile", {})
        communication_patterns = customer_analysis.get("communication_patterns", {})
        
        preferences_prompt = f"""
        Analyze customer preferences for HVAC equipment selection based on communication patterns.
        
        Customer Profile:
        - Communication Style: {customer_profile.get("communication_style", "unknown")}
        - Technical Knowledge: {customer_profile.get("technical_knowledge", "unknown")}
        - Decision Making Style: {customer_profile.get("decision_making_style", "unknown")}
        - Budget Indicators: {customer_profile.get("budget_indicators", "unknown")}
        
        Primary Concerns: {communication_patterns.get("primary_concerns", [])}
        
        Generate preferences analysis in JSON format:
        {{
            "brand_preferences": {{
                "preferred_brands": ["LG", "Daikin", "Mitsubishi", "any"],
                "brand_loyalty": "high|medium|low|none",
                "brand_concerns": ["reliability", "price", "features", "service"]
            }},
            "feature_priorities": {{
                "energy_efficiency": "critical|important|nice_to_have|not_important",
                "quiet_operation": "critical|important|nice_to_have|not_important",
                "smart_features": "critical|important|nice_to_have|not_important",
                "design_aesthetics": "critical|important|nice_to_have|not_important",
                "warranty_coverage": "critical|important|nice_to_have|not_important"
            }},
            "decision_factors": {{
                "primary_factor": "price|quality|features|brand|service",
                "secondary_factors": ["list", "of", "other", "important", "factors"],
                "deal_breakers": ["factors", "that", "would", "eliminate", "options"]
            }},
            "communication_preferences": {{
                "preferred_contact_method": "email|phone|text|in_person",
                "information_detail_level": "high|medium|low",
                "follow_up_frequency": "frequent|moderate|minimal",
                "technical_explanation_needed": true|false
            }},
            "service_expectations": {{
                "installation_service_priority": "high|medium|low",
                "maintenance_service_interest": "high|medium|low",
                "warranty_service_expectations": "comprehensive|standard|basic",
                "response_time_expectations": "immediate|same_day|next_day|flexible"
            }}
        }}
        """
        
        try:
            response = await self.run_task(preferences_prompt)
            preferences = json.loads(response)
            
            # Add preference scoring
            preferences["preference_confidence"] = self._calculate_preference_confidence(
                preferences, customer_analysis
            )
            
            return preferences
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to analyze customer preferences: {e}")
            
            # Return default preferences
            return {
                "brand_preferences": {"preferred_brands": ["any"], "brand_loyalty": "none"},
                "feature_priorities": {},
                "decision_factors": {"primary_factor": "price"},
                "communication_preferences": {},
                "service_expectations": {},
                "preference_confidence": 0.3
            }
    
    async def _analyze_business_potential(
        self,
        customer_analysis: Dict[str, Any],
        communication_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze business potential and customer value."""
        
        customer_profile = customer_analysis.get("customer_profile", {})
        technical_reqs = customer_analysis.get("technical_requirements", {})
        
        business_prompt = f"""
        Analyze business potential and customer value for this HVAC prospect.
        
        Customer Profile:
        - Budget Indicators: {customer_profile.get("budget_indicators", "unknown")}
        - Urgency Level: {customer_profile.get("urgency_level", "unknown")}
        - Decision Making Style: {customer_profile.get("decision_making_style", "unknown")}
        
        Technical Requirements:
        - Property Type: {technical_reqs.get("space_details", {}).get("property_type", "unknown")}
        - System Replacement Reason: {technical_reqs.get("current_system", {}).get("replacement_reason", "unknown")}
        
        Generate business analysis in JSON format:
        {{
            "customer_value": {{
                "estimated_project_value": {{
                    "min_value_pln": number,
                    "max_value_pln": number,
                    "most_likely_pln": number
                }},
                "lifetime_value_potential": "high|medium|low",
                "referral_potential": "high|medium|low",
                "repeat_business_likelihood": "high|medium|low"
            }},
            "sales_probability": {{
                "conversion_likelihood": 0.0-1.0,
                "timeline_to_decision": "days|weeks|months",
                "competition_level": "high|medium|low",
                "decision_maker_engagement": "high|medium|low"
            }},
            "risk_factors": {{
                "price_sensitivity_risk": "high|medium|low",
                "technical_complexity_risk": "high|medium|low",
                "timeline_pressure_risk": "high|medium|low",
                "budget_uncertainty_risk": "high|medium|low"
            }},
            "opportunity_factors": {{
                "upsell_opportunities": ["additional", "services", "or", "products"],
                "service_contract_potential": "high|medium|low",
                "maintenance_revenue_potential": "high|medium|low",
                "upgrade_future_potential": "high|medium|low"
            }},
            "recommended_approach": {{
                "sales_strategy": "consultative|value_based|price_competitive|relationship",
                "quote_positioning": "premium|value|budget",
                "follow_up_strategy": "aggressive|moderate|gentle",
                "presentation_style": "technical|benefits_focused|price_focused"
            }}
        }}
        """
        
        try:
            response = await self.run_task(business_prompt)
            business_analysis = json.loads(response)
            
            # Add business scoring
            business_analysis["business_confidence"] = self._calculate_business_confidence(
                business_analysis, customer_analysis
            )
            
            return business_analysis
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to analyze business potential: {e}")
            
            # Return basic business analysis
            return {
                "customer_value": {"estimated_project_value": {"most_likely_pln": 15000}},
                "sales_probability": {"conversion_likelihood": 0.5},
                "risk_factors": {},
                "opportunity_factors": {},
                "recommended_approach": {"sales_strategy": "consultative"},
                "business_confidence": 0.3
            }
    
    async def _generate_quote_recommendations(
        self,
        equipment_requirements: Dict[str, Any],
        preferences_analysis: Dict[str, Any],
        business_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate recommendations for quote creation."""
        
        return {
            "quote_strategy": {
                "positioning": business_analysis.get("recommended_approach", {}).get("quote_positioning", "value"),
                "presentation_style": business_analysis.get("recommended_approach", {}).get("presentation_style", "benefits_focused"),
                "emphasis_areas": self._determine_emphasis_areas(preferences_analysis),
                "customization_level": self._determine_customization_level(equipment_requirements)
            },
            "equipment_focus": {
                "system_type_priority": equipment_requirements.get("system_type", "split"),
                "feature_highlights": self._extract_feature_highlights(preferences_analysis),
                "technical_detail_level": self._determine_technical_level(preferences_analysis)
            },
            "pricing_strategy": {
                "price_sensitivity": business_analysis.get("risk_factors", {}).get("price_sensitivity_risk", "medium"),
                "value_proposition_focus": self._determine_value_focus(preferences_analysis),
                "financing_options": business_analysis.get("opportunity_factors", {}).get("service_contract_potential", "medium")
            },
            "follow_up_recommendations": {
                "timeline": business_analysis.get("sales_probability", {}).get("timeline_to_decision", "weeks"),
                "communication_method": preferences_analysis.get("communication_preferences", {}).get("preferred_contact_method", "email"),
                "information_level": preferences_analysis.get("communication_preferences", {}).get("information_detail_level", "medium")
            }
        }
    
    def _calculate_requirements_confidence(self, requirements: Dict[str, Any], analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for extracted requirements."""
        confidence = analysis.get("confidence_score", 0.5)
        
        # Adjust based on requirement completeness
        complete_sections = sum(1 for section in requirements.values() if section and isinstance(section, dict))
        completeness_bonus = min(0.3, complete_sections * 0.05)
        
        return min(1.0, confidence + completeness_bonus)
    
    def _calculate_preference_confidence(self, preferences: Dict[str, Any], analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for preference analysis."""
        base_confidence = analysis.get("confidence_score", 0.5)
        
        # Adjust based on communication style clarity
        comm_style = analysis.get("customer_profile", {}).get("communication_style", "casual")
        if comm_style in ["technical", "formal"]:
            return min(1.0, base_confidence + 0.2)
        
        return base_confidence
    
    def _calculate_business_confidence(self, business: Dict[str, Any], analysis: Dict[str, Any]) -> float:
        """Calculate confidence score for business analysis."""
        base_confidence = analysis.get("confidence_score", 0.5)
        
        # Adjust based on urgency and decision-making style
        urgency = analysis.get("customer_profile", {}).get("urgency_level", "planning")
        if urgency in ["immediate", "soon"]:
            return min(1.0, base_confidence + 0.15)
        
        return base_confidence
    
    def _determine_emphasis_areas(self, preferences: Dict[str, Any]) -> List[str]:
        """Determine what areas to emphasize in the quote."""
        emphasis = []
        
        feature_priorities = preferences.get("feature_priorities", {})
        for feature, priority in feature_priorities.items():
            if priority in ["critical", "important"]:
                emphasis.append(feature)
        
        return emphasis[:3]  # Top 3 emphasis areas
    
    def _determine_customization_level(self, requirements: Dict[str, Any]) -> str:
        """Determine level of customization needed."""
        confidence = requirements.get("requirements_confidence", 0.5)
        
        if confidence > 0.8:
            return "high"
        elif confidence > 0.5:
            return "medium"
        else:
            return "low"
    
    def _extract_feature_highlights(self, preferences: Dict[str, Any]) -> List[str]:
        """Extract features to highlight in equipment recommendations."""
        highlights = []
        
        feature_priorities = preferences.get("feature_priorities", {})
        for feature, priority in feature_priorities.items():
            if priority == "critical":
                highlights.append(feature)
        
        return highlights
    
    def _determine_technical_level(self, preferences: Dict[str, Any]) -> str:
        """Determine appropriate technical detail level."""
        info_level = preferences.get("communication_preferences", {}).get("information_detail_level", "medium")
        technical_needed = preferences.get("communication_preferences", {}).get("technical_explanation_needed", False)
        
        if technical_needed and info_level == "high":
            return "detailed"
        elif info_level == "high":
            return "moderate"
        else:
            return "basic"
    
    def _determine_value_focus(self, preferences: Dict[str, Any]) -> str:
        """Determine what value proposition to focus on."""
        primary_factor = preferences.get("decision_factors", {}).get("primary_factor", "price")
        
        value_mapping = {
            "price": "cost_savings",
            "quality": "reliability",
            "features": "functionality",
            "brand": "reputation",
            "service": "support"
        }
        
        return value_mapping.get(primary_factor, "overall_value")
    
    async def _fallback_communication_analysis(
        self,
        customer_id: str,
        communication_history: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Fallback analysis when AI analysis fails."""
        
        # Basic pattern matching analysis
        all_text = " ".join([comm.get("content", "") for comm in communication_history])
        
        # Simple keyword analysis
        urgency_keywords = ["urgent", "asap", "immediately", "emergency", "broken", "not working"]
        budget_keywords = ["cheap", "expensive", "budget", "cost", "price", "affordable"]
        technical_keywords = ["specifications", "efficiency", "kw", "btu", "seer", "cop"]
        
        urgency_score = sum(1 for keyword in urgency_keywords if keyword.lower() in all_text.lower())
        budget_mentions = sum(1 for keyword in budget_keywords if keyword.lower() in all_text.lower())
        technical_mentions = sum(1 for keyword in technical_keywords if keyword.lower() in all_text.lower())
        
        return {
            "customer_profile": {
                "urgency_level": "immediate" if urgency_score > 2 else "planning",
                "budget_indicators": "budget" if budget_mentions > 2 else "standard",
                "technical_knowledge": "high" if technical_mentions > 3 else "low"
            },
            "communication_patterns": {
                "primary_concerns": ["equipment_replacement"] if urgency_score > 0 else ["planning"],
                "urgency_indicators": urgency_score,
                "budget_sensitivity": budget_mentions
            },
            "sentiment_analysis": {
                "overall_sentiment": "negative" if urgency_score > 1 else "neutral"
            },
            "technical_requirements": {
                "space_details": {"property_type": "residential"},
                "specific_needs": {"cooling_requirements": "medium"}
            },
            "confidence_score": 0.3,
            "analysis_notes": "Fallback pattern-based analysis"
        }
