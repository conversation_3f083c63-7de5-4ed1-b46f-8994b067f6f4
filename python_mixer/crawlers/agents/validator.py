
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Validator Agent for validating extracted HVAC data quality and completeness."""

import re
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
from loguru import logger

from .base_agent import BaseAgent
from models import HVACEquipment, EquipmentType, EnergyClass


class ValidatorAgent(BaseAgent):
    """Agent responsible for validating extracted HVAC equipment data."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="Validator",
            role="HVAC Data Quality Assurance Specialist",
            goal="Ensure accuracy, completeness, and consistency of extracted HVAC equipment data",
            backstory=(
                "You are meticulous about data quality and have extensive knowledge of "
                "HVAC specifications, industry standards, and technical parameters. "
                "Your expertise helps identify data inconsistencies, validate technical "
                "specifications, and ensure that only high-quality data enters the system."
            ),
            **kwargs
        )
        
        # Validation rules and thresholds
        self.validation_rules = {
            "min_confidence": 0.3,
            "required_fields": ["manufacturer", "model", "type"],
            "capacity_ranges": {
                "cooling_capacity": {"min": 0.5, "max": 1000, "unit": "kW"},
                "heating_capacity": {"min": 0.5, "max": 1000, "unit": "kW"}
            },
            "efficiency_ranges": {
                "cooling_eer": {"min": 1.0, "max": 10.0},
                "heating_cop": {"min": 1.0, "max": 8.0},
                "seer": {"min": 8.0, "max": 30.0},
                "scop": {"min": 2.0, "max": 8.0}
            },
            "dimension_ranges": {
                "width": {"min": 100, "max": 5000, "unit": "mm"},
                "height": {"min": 100, "max": 5000, "unit": "mm"},
                "depth": {"min": 100, "max": 2000, "unit": "mm"}
            },
            "weight_ranges": {
                "min": 5, "max": 1000, "unit": "kg"
            }
        }
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute validation of extracted HVAC equipment data.
        
        Args:
            input_data: Extracted equipment data and validation configuration
            **kwargs: Additional validation parameters
            
        Returns:
            Validation results with validated data and quality report
        """
        try:
            logger.info("Starting HVAC data validation")
            
            # Validate input
            if not self.validate_input(input_data, ["extracted_equipment"]):
                raise ValueError("Invalid input data for validation")
            
            extracted_equipment = input_data["extracted_equipment"]
            validation_rules = input_data.get("validation_rules", {})
            
            # Update validation rules with custom rules
            self._update_validation_rules(validation_rules)
            
            # Perform comprehensive validation
            validation_results = await self._perform_comprehensive_validation(extracted_equipment)
            
            # Generate validation report
            validation_report = await self._generate_validation_report(validation_results)
            
            # Filter validated equipment
            validated_equipment = self._filter_validated_equipment(validation_results)
            
            # Compile final results
            final_results = {
                "validation_summary": {
                    "total_equipment_processed": len(extracted_equipment),
                    "equipment_validated": len(validated_equipment),
                    "validation_rate": len(validated_equipment) / max(1, len(extracted_equipment)),
                    "timestamp": datetime.now().isoformat(),
                    "validator_version": "1.0.0"
                },
                "validated_equipment": validated_equipment,
                "validation_report": validation_report,
                "quality_metrics": await self._calculate_quality_metrics(validated_equipment)
            }
            
            logger.info(f"Data validation completed: {len(validated_equipment)} equipment items validated")
            return final_results
            
        except Exception as e:
            logger.error(f"Data validation failed: {e}")
            raise
    
    def _update_validation_rules(self, custom_rules: Dict[str, Any]):
        """Update validation rules with custom configuration."""
        
        if "min_confidence" in custom_rules:
            self.validation_rules["min_confidence"] = custom_rules["min_confidence"]
        
        if "required_fields" in custom_rules:
            self.validation_rules["required_fields"] = custom_rules["required_fields"]
        
        # Update other rules as needed
        for rule_category in ["capacity_ranges", "efficiency_ranges", "dimension_ranges"]:
            if rule_category in custom_rules:
                self.validation_rules[rule_category].update(custom_rules[rule_category])
    
    async def _perform_comprehensive_validation(
        self,
        equipment_list: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Perform comprehensive validation on all equipment items."""
        
        validation_results = []
        
        for i, equipment in enumerate(equipment_list):
            try:
                # Perform individual equipment validation
                validation_result = await self._validate_single_equipment(equipment, i)
                validation_results.append(validation_result)
                
            except Exception as e:
                logger.error(f"Failed to validate equipment {i}: {e}")
                validation_results.append({
                    "equipment": equipment,
                    "index": i,
                    "valid": False,
                    "errors": [f"Validation error: {str(e)}"],
                    "warnings": [],
                    "score": 0.0,
                    "issues": []
                })
        
        # Perform cross-validation checks
        await self._perform_cross_validation(validation_results)
        
        return validation_results
    
    async def _validate_single_equipment(
        self,
        equipment: Dict[str, Any],
        index: int
    ) -> Dict[str, Any]:
        """Validate a single equipment item."""
        
        validation_result = {
            "equipment": equipment,
            "index": index,
            "valid": True,
            "errors": [],
            "warnings": [],
            "score": 0.0,
            "issues": []
        }
        
        # Basic structure validation
        await self._validate_basic_structure(equipment, validation_result)
        
        # Required fields validation
        await self._validate_required_fields(equipment, validation_result)
        
        # Technical specifications validation
        await self._validate_technical_specs(equipment, validation_result)
        
        # Data consistency validation
        await self._validate_data_consistency(equipment, validation_result)
        
        # Confidence score validation
        await self._validate_confidence_score(equipment, validation_result)
        
        # Calculate overall validation score
        validation_result["score"] = await self._calculate_validation_score(validation_result)
        
        # Determine if equipment is valid
        validation_result["valid"] = (
            len(validation_result["errors"]) == 0 and
            validation_result["score"] >= self.validation_rules["min_confidence"]
        )
        
        return validation_result
    
    async def _validate_basic_structure(
        self,
        equipment: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate basic equipment data structure."""
        
        # Check for basic_info section
        if "basic_info" not in equipment:
            result["errors"].append("Missing 'basic_info' section")
            return
        
        basic_info = equipment["basic_info"]
        
        # Validate equipment type
        equipment_type = basic_info.get("type")
        if equipment_type:
            try:
                EquipmentType(equipment_type)
            except ValueError:
                result["warnings"].append(f"Unknown equipment type: {equipment_type}")
        
        # Validate manufacturer name
        manufacturer = basic_info.get("manufacturer")
        if manufacturer and len(manufacturer) < 2:
            result["warnings"].append("Manufacturer name is too short")
        
        # Validate model name
        model = basic_info.get("model")
        if model and len(model) < 2:
            result["warnings"].append("Model name is too short")
        
        # Validate release year
        release_year = basic_info.get("release_year")
        if release_year:
            current_year = datetime.now().year
            if not (1900 <= release_year <= current_year + 2):
                result["warnings"].append(f"Invalid release year: {release_year}")
    
    async def _validate_required_fields(
        self,
        equipment: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate required fields are present and valid."""
        
        basic_info = equipment.get("basic_info", {})
        
        for field in self.validation_rules["required_fields"]:
            if field not in basic_info or not basic_info[field]:
                result["errors"].append(f"Missing required field: {field}")
            elif isinstance(basic_info[field], str) and len(basic_info[field].strip()) == 0:
                result["errors"].append(f"Empty required field: {field}")
    
    async def _validate_technical_specs(
        self,
        equipment: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate technical specifications."""
        
        tech_specs = equipment.get("technical_specs", {})
        if not tech_specs:
            result["warnings"].append("No technical specifications provided")
            return
        
        # Validate capacity values
        await self._validate_capacity_values(tech_specs, result)
        
        # Validate energy efficiency values
        await self._validate_efficiency_values(tech_specs, result)
        
        # Validate dimensions
        await self._validate_dimensions(tech_specs, result)
        
        # Validate weight
        await self._validate_weight(tech_specs, result)
        
        # Validate refrigerant
        await self._validate_refrigerant(tech_specs, result)
        
        # Validate electrical specifications
        await self._validate_electrical_specs(tech_specs, result)
    
    async def _validate_capacity_values(
        self,
        tech_specs: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate capacity values."""
        
        for capacity_type in ["cooling_capacity", "heating_capacity"]:
            capacity = tech_specs.get(capacity_type)
            if not capacity:
                continue
            
            if not isinstance(capacity, dict):
                result["warnings"].append(f"Invalid {capacity_type} format")
                continue
            
            value = capacity.get("value")
            unit = capacity.get("unit", "").lower()
            
            if value is not None:
                # Convert to kW for validation
                kw_value = self._convert_to_kw(value, unit)
                
                if kw_value is not None:
                    ranges = self.validation_rules["capacity_ranges"][capacity_type]
                    if not (ranges["min"] <= kw_value <= ranges["max"]):
                        result["warnings"].append(
                            f"{capacity_type} value {kw_value} kW is outside normal range "
                            f"({ranges['min']}-{ranges['max']} kW)"
                        )
                else:
                    result["warnings"].append(f"Unknown unit for {capacity_type}: {unit}")
    
    async def _validate_efficiency_values(
        self,
        tech_specs: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate energy efficiency values."""
        
        efficiency = tech_specs.get("energy_efficiency", {})
        if not efficiency:
            return
        
        for eff_type, ranges in self.validation_rules["efficiency_ranges"].items():
            value = efficiency.get(eff_type)
            if value is not None:
                if not (ranges["min"] <= value <= ranges["max"]):
                    result["warnings"].append(
                        f"{eff_type} value {value} is outside normal range "
                        f"({ranges['min']}-{ranges['max']})"
                    )
        
        # Validate energy class
        energy_class = efficiency.get("energy_class")
        if energy_class:
            try:
                EnergyClass(energy_class)
            except ValueError:
                result["warnings"].append(f"Invalid energy class: {energy_class}")
    
    async def _validate_dimensions(
        self,
        tech_specs: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate physical dimensions."""
        
        dimensions = tech_specs.get("dimensions", {})
        if not dimensions:
            return
        
        for dim_type in ["width", "height", "depth"]:
            value = dimensions.get(dim_type)
            if value is not None:
                ranges = self.validation_rules["dimension_ranges"][dim_type]
                if not (ranges["min"] <= value <= ranges["max"]):
                    result["warnings"].append(
                        f"{dim_type} value {value} mm is outside normal range "
                        f"({ranges['min']}-{ranges['max']} mm)"
                    )
    
    async def _validate_weight(
        self,
        tech_specs: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate weight specifications."""
        
        weight = tech_specs.get("weight", {})
        if not weight:
            return
        
        value = weight.get("value")
        if value is not None:
            ranges = self.validation_rules["weight_ranges"]
            if not (ranges["min"] <= value <= ranges["max"]):
                result["warnings"].append(
                    f"Weight value {value} kg is outside normal range "
                    f"({ranges['min']}-{ranges['max']} kg)"
                )
    
    async def _validate_refrigerant(
        self,
        tech_specs: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate refrigerant specifications."""
        
        refrigerant = tech_specs.get("refrigerant", {})
        if not refrigerant:
            return
        
        ref_type = refrigerant.get("type")
        if ref_type:
            # Common refrigerant types
            common_refrigerants = [
                "R32", "R410A", "R134a", "R407C", "R290", "R600a", "R744", "R1234yf", "R1234ze"
            ]
            
            if ref_type.upper() not in [r.upper() for r in common_refrigerants]:
                result["warnings"].append(f"Uncommon refrigerant type: {ref_type}")
        
        charge = refrigerant.get("charge")
        if charge is not None and (charge < 0.1 or charge > 50):
            result["warnings"].append(f"Unusual refrigerant charge: {charge} kg")
    
    async def _validate_electrical_specs(
        self,
        tech_specs: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate electrical specifications."""
        
        electrical = tech_specs.get("electrical", {})
        if not electrical:
            return
        
        # Validate power supply format
        power_supply = electrical.get("power_supply")
        if power_supply:
            # Common patterns: "230V 50Hz", "400V 3Ph 50Hz", etc.
            if not re.match(r'\d+V.*\d+Hz', power_supply):
                result["warnings"].append(f"Unusual power supply format: {power_supply}")
        
        # Validate power consumption values
        for power_type in ["cooling_power", "heating_power"]:
            power = electrical.get(power_type)
            if power is not None and (power < 100 or power > 50000):  # Watts
                result["warnings"].append(f"Unusual {power_type}: {power} W")
    
    async def _validate_data_consistency(
        self,
        equipment: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate data consistency and logical relationships."""
        
        tech_specs = equipment.get("technical_specs", {})
        
        # Check capacity vs power consistency
        cooling_capacity = tech_specs.get("cooling_capacity", {})
        electrical = tech_specs.get("electrical", {})
        
        if (cooling_capacity.get("value") and electrical.get("cooling_power") and
            cooling_capacity.get("unit", "").lower() == "kw"):
            
            capacity_kw = cooling_capacity["value"]
            power_kw = electrical["cooling_power"] / 1000  # Convert W to kW
            
            if power_kw > 0:
                eer = capacity_kw / power_kw
                if eer < 1.0 or eer > 10.0:
                    result["warnings"].append(
                        f"Inconsistent capacity/power ratio (EER: {eer:.2f})"
                    )
        
        # Check dimension consistency
        dimensions = tech_specs.get("dimensions", {})
        if dimensions:
            width = dimensions.get("width", 0)
            height = dimensions.get("height", 0)
            depth = dimensions.get("depth", 0)
            
            if width and height and depth:
                # Check for unrealistic aspect ratios
                max_dim = max(width, height, depth)
                min_dim = min(width, height, depth)
                
                if max_dim / min_dim > 20:
                    result["warnings"].append("Unusual dimension aspect ratio")
    
    async def _validate_confidence_score(
        self,
        equipment: Dict[str, Any],
        result: Dict[str, Any]
    ):
        """Validate confidence score."""
        
        confidence = equipment.get("confidence_score", 0)
        
        if not (0.0 <= confidence <= 1.0):
            result["errors"].append(f"Invalid confidence score: {confidence}")
        elif confidence < self.validation_rules["min_confidence"]:
            result["warnings"].append(f"Low confidence score: {confidence}")
    
    async def _calculate_validation_score(self, result: Dict[str, Any]) -> float:
        """Calculate overall validation score for equipment."""
        
        equipment = result["equipment"]
        
        # Base score from confidence
        base_score = equipment.get("confidence_score", 0.5)
        
        # Penalty for errors (each error reduces score by 0.2)
        error_penalty = len(result["errors"]) * 0.2
        
        # Penalty for warnings (each warning reduces score by 0.05)
        warning_penalty = len(result["warnings"]) * 0.05
        
        # Bonus for completeness
        completeness_bonus = self._calculate_completeness_bonus(equipment)
        
        # Calculate final score
        final_score = max(0.0, min(1.0, base_score - error_penalty - warning_penalty + completeness_bonus))
        
        return round(final_score, 3)
    
    def _calculate_completeness_bonus(self, equipment: Dict[str, Any]) -> float:
        """Calculate bonus score based on data completeness."""
        
        bonus = 0.0
        
        # Bonus for having technical specs
        tech_specs = equipment.get("technical_specs", {})
        if tech_specs:
            bonus += 0.1
            
            # Bonus for specific technical data
            if tech_specs.get("cooling_capacity"):
                bonus += 0.05
            if tech_specs.get("energy_efficiency"):
                bonus += 0.05
            if tech_specs.get("dimensions"):
                bonus += 0.03
            if tech_specs.get("refrigerant"):
                bonus += 0.02
        
        # Bonus for features and certifications
        if equipment.get("features"):
            bonus += 0.02
        if equipment.get("certifications"):
            bonus += 0.02
        
        # Bonus for documentation
        if equipment.get("documentation"):
            bonus += 0.01
        
        return min(0.2, bonus)  # Cap bonus at 0.2
    
    async def _perform_cross_validation(self, validation_results: List[Dict[str, Any]]):
        """Perform cross-validation checks across all equipment."""
        
        # Check for duplicates
        seen_equipment = set()
        
        for result in validation_results:
            equipment = result["equipment"]
            basic_info = equipment.get("basic_info", {})
            
            # Create unique key
            key = (
                basic_info.get("manufacturer", "").lower(),
                basic_info.get("model", "").lower(),
                basic_info.get("type", "").lower()
            )
            
            if key in seen_equipment and all(key):
                result["warnings"].append("Potential duplicate equipment detected")
            else:
                seen_equipment.add(key)
    
    def _filter_validated_equipment(
        self,
        validation_results: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Filter and return only validated equipment."""
        
        validated_equipment = []
        
        for result in validation_results:
            if result["valid"]:
                equipment = result["equipment"].copy()
                
                # Add validation metadata
                equipment["validation_metadata"] = {
                    "validated_at": datetime.now().isoformat(),
                    "validator": "ValidatorAgent",
                    "validation_score": result["score"],
                    "warnings_count": len(result["warnings"]),
                    "issues_resolved": len(result.get("issues", []))
                }
                
                validated_equipment.append(equipment)
        
        return validated_equipment
    
    async def _generate_validation_report(
        self,
        validation_results: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate comprehensive validation report."""
        
        total_items = len(validation_results)
        valid_items = len([r for r in validation_results if r["valid"]])
        
        # Collect all errors and warnings
        all_errors = []
        all_warnings = []
        
        for result in validation_results:
            all_errors.extend(result["errors"])
            all_warnings.extend(result["warnings"])
        
        # Calculate statistics
        scores = [r["score"] for r in validation_results]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        report = {
            "summary": {
                "total_items": total_items,
                "valid_items": valid_items,
                "invalid_items": total_items - valid_items,
                "validation_rate": valid_items / total_items if total_items > 0 else 0,
                "average_score": round(avg_score, 3)
            },
            "issues": {
                "total_errors": len(all_errors),
                "total_warnings": len(all_warnings),
                "common_errors": self._get_common_issues(all_errors),
                "common_warnings": self._get_common_issues(all_warnings)
            },
            "quality_distribution": {
                "high_quality": len([s for s in scores if s >= 0.8]),
                "medium_quality": len([s for s in scores if 0.5 <= s < 0.8]),
                "low_quality": len([s for s in scores if s < 0.5])
            },
            "recommendations": await self._generate_validation_recommendations(validation_results)
        }
        
        return report
    
    def _get_common_issues(self, issues: List[str]) -> List[Dict[str, Any]]:
        """Get most common issues and their frequencies."""
        
        issue_counts = {}
        for issue in issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        # Sort by frequency and return top 10
        sorted_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)
        
        return [
            {"issue": issue, "count": count}
            for issue, count in sorted_issues[:10]
        ]
    
    async def _generate_validation_recommendations(
        self,
        validation_results: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate recommendations based on validation results."""
        
        recommendations = []
        
        # Calculate validation rate
        total_items = len(validation_results)
        valid_items = len([r for r in validation_results if r["valid"]])
        validation_rate = valid_items / total_items if total_items > 0 else 0
        
        if validation_rate < 0.7:
            recommendations.append(
                f"Low validation rate ({validation_rate:.1%}). "
                "Consider improving data extraction quality or adjusting validation rules."
            )
        
        # Check for common errors
        all_errors = []
        for result in validation_results:
            all_errors.extend(result["errors"])
        
        if "Missing required field" in str(all_errors):
            recommendations.append(
                "Many items are missing required fields. "
                "Review extraction algorithms to ensure basic information is captured."
            )
        
        # Check average score
        scores = [r["score"] for r in validation_results]
        avg_score = sum(scores) / len(scores) if scores else 0
        
        if avg_score < 0.6:
            recommendations.append(
                f"Low average validation score ({avg_score:.3f}). "
                "Focus on improving data completeness and accuracy."
            )
        
        return recommendations
    
    def _convert_to_kw(self, value: float, unit: str) -> Optional[float]:
        """Convert capacity value to kW."""
        
        unit = unit.lower().strip()
        
        if unit in ["kw", "kilowatt", "kilowatts"]:
            return value
        elif unit in ["w", "watt", "watts"]:
            return value / 1000
        elif unit in ["btu/h", "btu", "btuh"]:
            return value * 0.000293071
        elif unit in ["hp", "horsepower"]:
            return value * 0.746
        else:
            return None
    
    async def _calculate_quality_metrics(
        self,
        validated_equipment: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Calculate quality metrics for validated equipment."""
        
        if not validated_equipment:
            return {
                "total_validated": 0,
                "average_confidence": 0,
                "completeness_rate": 0,
                "high_quality_items": 0
            }
        
        # Calculate metrics
        confidences = [eq.get("confidence_score", 0) for eq in validated_equipment]
        avg_confidence = sum(confidences) / len(confidences)
        
        # Count complete items
        complete_items = 0
        for equipment in validated_equipment:
            basic_info = equipment.get("basic_info", {})
            tech_specs = equipment.get("technical_specs", {})
            
            if (basic_info.get("manufacturer") and basic_info.get("model") and
                tech_specs and len(tech_specs) > 2):
                complete_items += 1
        
        completeness_rate = complete_items / len(validated_equipment)
        high_quality_items = len([c for c in confidences if c > 0.8])
        
        return {
            "total_validated": len(validated_equipment),
            "average_confidence": round(avg_confidence, 3),
            "completeness_rate": round(completeness_rate, 3),
            "high_quality_items": high_quality_items,
            "quality_distribution": {
                "excellent": len([c for c in confidences if c >= 0.9]),
                "good": len([c for c in confidences if 0.7 <= c < 0.9]),
                "fair": len([c for c in confidences if 0.5 <= c < 0.7]),
                "poor": len([c for c in confidences if c < 0.5])
            }
        }
