
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Crawler Agent for executing web crawling operations."""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .base_agent import BaseAgent
from crawlers import CrawlerManager


class CrawlerAgent(BaseAgent):
    """Agent responsible for executing web crawling operations."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="Crawler",
            role="Web Crawling Specialist",
            goal="Execute efficient and comprehensive web crawling for HVAC equipment data",
            backstory=(
                "You are a specialist in web crawling and data extraction with expertise "
                "in navigating complex technical documentation and product catalogs. "
                "You understand website structures, can handle dynamic content, and "
                "know how to extract maximum value while respecting website policies."
            ),
            **kwargs
        )
        
        # Initialize crawler manager
        self.crawler_manager = CrawlerManager()
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute crawling operations based on the provided plan.
        
        Args:
            input_data: Crawling plan and configuration
            **kwargs: Additional crawling parameters
            
        Returns:
            Crawling results with extracted content
        """
        try:
            logger.info("Starting web crawling execution")
            
            # Validate input
            if not self.validate_input(input_data, ["plan", "manufacturers"]):
                raise ValueError("Invalid input data for crawling")
            
            plan = input_data["plan"]
            manufacturers = plan.get("manufacturers", [])
            
            # Execute crawling for each manufacturer
            results = await self._execute_crawling_plan(manufacturers, **kwargs)
            
            # Compile final results
            final_results = {
                "execution_summary": {
                    "total_manufacturers": len(manufacturers),
                    "successful_crawls": len([r for r in results if r["success"]]),
                    "failed_crawls": len([r for r in results if not r["success"]]),
                    "total_pages_crawled": sum(r.get("pages_crawled", 0) for r in results),
                    "total_data_extracted": sum(r.get("data_count", 0) for r in results),
                    "execution_time": sum(r.get("duration_seconds", 0) for r in results),
                    "timestamp": datetime.now().isoformat()
                },
                "manufacturer_results": results,
                "raw_data": [item for r in results for item in r.get("extracted_data", [])]
            }
            
            logger.info(f"Crawling execution completed: {final_results['execution_summary']}")
            return final_results
            
        except Exception as e:
            logger.error(f"Crawling execution failed: {e}")
            raise
    
    async def _execute_crawling_plan(
        self,
        manufacturers: List[Dict[str, Any]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Execute crawling plan for all manufacturers."""
        
        results = []
        
        # Sort manufacturers by crawl order
        sorted_manufacturers = sorted(
            manufacturers,
            key=lambda x: x.get("crawl_order", 999)
        )
        
        for manufacturer in sorted_manufacturers:
            try:
                logger.info(f"Starting crawling for {manufacturer['name']}")
                
                # Execute crawling for this manufacturer
                result = await self._crawl_manufacturer(manufacturer, **kwargs)
                results.append(result)
                
                # Add delay between manufacturers
                if len(results) < len(manufacturers):
                    await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Failed to crawl {manufacturer['name']}: {e}")
                results.append({
                    "manufacturer": manufacturer["name"],
                    "success": False,
                    "error": str(e),
                    "pages_crawled": 0,
                    "data_count": 0,
                    "duration_seconds": 0,
                    "extracted_data": []
                })
        
        return results
    
    async def _crawl_manufacturer(
        self,
        manufacturer: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Crawl a specific manufacturer's website."""
        
        start_time = datetime.now()
        
        try:
            # Get target URLs
            target_urls = manufacturer.get("target_urls", [])
            if not target_urls:
                # Generate URLs from base configuration
                target_urls = self._generate_target_urls(manufacturer)
            
            # Get crawling parameters
            crawl_params = await self._get_crawling_parameters(manufacturer)
            
            # Execute crawling
            crawl_results = await self.crawler_manager.crawl_urls(
                urls=target_urls,
                **crawl_params
            )
            
            # Process and extract data
            extracted_data = await self._process_crawl_results(
                crawl_results,
                manufacturer
            )
            
            duration = (datetime.now() - start_time).total_seconds()
            
            return {
                "manufacturer": manufacturer["name"],
                "success": True,
                "pages_crawled": len(crawl_results),
                "data_count": len(extracted_data),
                "duration_seconds": duration,
                "extracted_data": extracted_data,
                "crawl_summary": {
                    "target_urls": target_urls,
                    "successful_pages": len([r for r in crawl_results if r.get("success")]),
                    "failed_pages": len([r for r in crawl_results if not r.get("success")]),
                    "average_page_size": sum(len(r.get("content", "")) for r in crawl_results) / max(1, len(crawl_results))
                }
            }
            
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"Manufacturer crawling failed for {manufacturer['name']}: {e}")
            
            return {
                "manufacturer": manufacturer["name"],
                "success": False,
                "error": str(e),
                "pages_crawled": 0,
                "data_count": 0,
                "duration_seconds": duration,
                "extracted_data": []
            }
    
    def _generate_target_urls(self, manufacturer: Dict[str, Any]) -> List[str]:
        """Generate target URLs for a manufacturer."""
        
        base_url = manufacturer.get("base_url", "")
        product_pages = manufacturer.get("product_pages", [])
        
        urls = []
        
        for page in product_pages:
            if page.startswith("http"):
                urls.append(page)
            else:
                urls.append(f"{base_url.rstrip('/')}/{page.lstrip('/')}")
        
        # Add base URL if no specific pages
        if not urls and base_url:
            urls.append(base_url)
        
        return urls
    
    async def _get_crawling_parameters(
        self,
        manufacturer: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get AI-optimized crawling parameters for a manufacturer."""
        
        # Create parameter optimization prompt
        optimization_prompt = f"""
        Optimize crawling parameters for {manufacturer['name']} HVAC equipment data extraction.
        
        Manufacturer configuration:
        {json.dumps(manufacturer, indent=2)}
        
        Available parameters:
        - max_pages: Maximum pages to crawl (current: {manufacturer.get('estimated_pages', 20)})
        - delay: Delay between requests in seconds
        - timeout: Request timeout in seconds
        - selectors: CSS selectors for data extraction
        - deep_crawl: Enable deep crawling (bfs/dfs)
        - content_filter: Content filtering rules
        
        Provide optimized parameters in JSON format:
        {{
            "max_pages": number,
            "delay": number,
            "timeout": number,
            "selectors": {{
                "product_list": "CSS selector",
                "product_specs": "CSS selector",
                "product_details": "CSS selector"
            }},
            "deep_crawl": "bfs|dfs|false",
            "content_filter": ["filter", "rules"],
            "extraction_rules": {{
                "focus_areas": ["technical specs", "product info"],
                "ignore_areas": ["navigation", "footer", "ads"]
            }}
        }}
        
        Focus on maximizing HVAC technical data extraction while respecting the website.
        """
        
        try:
            response = await self.run_task(optimization_prompt)
            params = json.loads(response)
            
            # Validate and set defaults
            return {
                "max_pages": min(params.get("max_pages", 20), 50),
                "delay": max(params.get("delay", 1.0), 0.5),
                "timeout": params.get("timeout", 30),
                "selectors": params.get("selectors", manufacturer.get("selectors", {})),
                "deep_crawl": params.get("deep_crawl", "bfs"),
                "content_filter": params.get("content_filter", []),
                "extraction_rules": params.get("extraction_rules", {})
            }
            
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"Failed to get AI parameters for {manufacturer['name']}: {e}")
            
            # Return default parameters
            return {
                "max_pages": manufacturer.get("estimated_pages", 20),
                "delay": 1.0,
                "timeout": 30,
                "selectors": manufacturer.get("selectors", {}),
                "deep_crawl": "bfs",
                "content_filter": [],
                "extraction_rules": {}
            }
    
    async def _process_crawl_results(
        self,
        crawl_results: List[Dict[str, Any]],
        manufacturer: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Process crawl results and extract structured data."""
        
        extracted_data = []
        
        for result in crawl_results:
            if not result.get("success") or not result.get("content"):
                continue
            
            try:
                # Extract data using AI
                data_items = await self._extract_data_from_content(
                    result["content"],
                    result["url"],
                    manufacturer
                )
                
                extracted_data.extend(data_items)
                
            except Exception as e:
                logger.error(f"Failed to extract data from {result.get('url')}: {e}")
        
        return extracted_data
    
    async def _extract_data_from_content(
        self,
        content: str,
        url: str,
        manufacturer: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract HVAC equipment data from page content using AI."""
        
        extraction_prompt = f"""
        Extract HVAC equipment technical data from the following webpage content.
        
        Source URL: {url}
        Manufacturer: {manufacturer['name']}
        
        Content (first 4000 characters):
        {content[:4000]}
        
        Extract all HVAC equipment with their technical specifications in JSON format:
        [
            {{
                "manufacturer": "{manufacturer['name']}",
                "model": "equipment model",
                "type": "equipment type (air_conditioner, heat_pump, etc.)",
                "series": "product series",
                "cooling_capacity": {{"value": number, "unit": "kW|BTU/h"}},
                "heating_capacity": {{"value": number, "unit": "kW|BTU/h"}},
                "energy_efficiency": {{
                    "cooling_eer": number,
                    "heating_cop": number,
                    "seer": number,
                    "scop": number,
                    "energy_class": "A+++|A++|A+|A|B|C|D"
                }},
                "dimensions": {{"width": number, "height": number, "depth": number, "unit": "mm"}},
                "weight": {{"value": number, "unit": "kg"}},
                "refrigerant": {{"type": "R32|R410A|etc", "charge": number, "unit": "kg"}},
                "electrical": {{
                    "power_supply": "230V 50Hz",
                    "cooling_power": number,
                    "heating_power": number,
                    "unit": "W"
                }},
                "features": ["list", "of", "features"],
                "source_url": "{url}",
                "extraction_confidence": 0.0-1.0
            }}
        ]
        
        Focus on technical specifications and numerical data. If some data is missing, omit those fields.
        Return empty array if no HVAC equipment is found.
        """
        
        try:
            response = await self.run_task(extraction_prompt)
            
            # Parse JSON response
            data_items = json.loads(response)
            
            # Validate and clean data
            validated_items = []
            for item in data_items:
                if self._validate_extracted_item(item):
                    # Add metadata
                    item["extraction_date"] = datetime.now().isoformat()
                    item["extractor"] = "CrawlerAgent"
                    validated_items.append(item)
            
            return validated_items
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to extract data from content: {e}")
            return []
    
    def _validate_extracted_item(self, item: Dict[str, Any]) -> bool:
        """Validate extracted equipment item."""
        
        required_fields = ["manufacturer", "model"]
        
        for field in required_fields:
            if not item.get(field):
                return False
        
        # Check if it looks like HVAC equipment
        hvac_indicators = [
            "air_conditioner", "heat_pump", "cooling", "heating",
            "hvac", "climate", "refrigerant", "btu", "kw"
        ]
        
        item_text = json.dumps(item).lower()
        has_hvac_indicator = any(indicator in item_text for indicator in hvac_indicators)
        
        return has_hvac_indicator
