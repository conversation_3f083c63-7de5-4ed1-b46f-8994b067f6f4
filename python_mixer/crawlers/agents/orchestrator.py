
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Agent Orchestrator for coordinating the complete HVAC data enrichment workflow."""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .planner import PlannerAgent
from .crawler import CrawlerAgent
from .extractor import ExtractorAgent
from .integrator import IntegratorAgent
from .validator import ValidatorAgent
from database import DatabaseManager


class AgentOrchestrator:
    """Orchestrates the complete HVAC data enrichment workflow using multiple agents."""
    
    def __init__(self):
        # Initialize agents
        self.planner = PlannerAgent()
        self.crawler = CrawlerAgent()
        self.extractor = ExtractorAgent()
        self.integrator = IntegratorAgent()
        self.validator = ValidatorAgent()
        
        # Initialize database manager
        self.database_manager = DatabaseManager()
        
        # Workflow state
        self.workflow_state = {
            "status": "idle",
            "current_phase": None,
            "start_time": None,
            "end_time": None,
            "results": {},
            "errors": []
        }
    
    async def initialize(self):
        """Initialize the orchestrator and all components."""
        try:
            logger.info("Initializing Agent Orchestrator")
            
            # Initialize database manager
            await self.database_manager.initialize()
            
            # Perform health checks on all agents
            health_results = await self._perform_health_checks()
            
            if not all(result.get("healthy", False) for result in health_results.values()):
                logger.warning("Some agents failed health checks")
                for agent, result in health_results.items():
                    if not result.get("healthy", False):
                        logger.warning(f"{agent}: {result.get('error', 'Unknown error')}")
            
            logger.info("Agent Orchestrator initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Agent Orchestrator: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup orchestrator resources."""
        try:
            await self.database_manager.cleanup()
            logger.info("Agent Orchestrator cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during orchestrator cleanup: {e}")
    
    async def execute_enrichment_workflow(
        self,
        manufacturers: List[str],
        priorities: Optional[Dict[str, int]] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Execute the complete HVAC data enrichment workflow.
        
        Args:
            manufacturers: List of manufacturer names to process
            priorities: Priority mapping for manufacturers (1=highest, 5=lowest)
            config: Additional configuration options
            
        Returns:
            Complete workflow results
        """
        workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            logger.info(f"Starting HVAC data enrichment workflow: {workflow_id}")
            
            # Initialize workflow state
            self.workflow_state = {
                "id": workflow_id,
                "status": "running",
                "current_phase": "planning",
                "start_time": datetime.now(),
                "end_time": None,
                "results": {},
                "errors": [],
                "config": config or {}
            }
            
            # Phase 1: Planning
            planning_results = await self._execute_planning_phase(manufacturers, priorities)
            self.workflow_state["results"]["planning"] = planning_results
            
            # Phase 2: Crawling
            self.workflow_state["current_phase"] = "crawling"
            crawling_results = await self._execute_crawling_phase(planning_results)
            self.workflow_state["results"]["crawling"] = crawling_results
            
            # Phase 3: Extraction
            self.workflow_state["current_phase"] = "extraction"
            extraction_results = await self._execute_extraction_phase(crawling_results)
            self.workflow_state["results"]["extraction"] = extraction_results
            
            # Phase 4: Validation
            self.workflow_state["current_phase"] = "validation"
            validation_results = await self._execute_validation_phase(extraction_results)
            self.workflow_state["results"]["validation"] = validation_results
            
            # Phase 5: Integration
            self.workflow_state["current_phase"] = "integration"
            integration_results = await self._execute_integration_phase(validation_results)
            self.workflow_state["results"]["integration"] = integration_results
            
            # Complete workflow
            self.workflow_state["status"] = "completed"
            self.workflow_state["current_phase"] = "completed"
            self.workflow_state["end_time"] = datetime.now()
            
            # Generate final summary
            final_summary = await self._generate_workflow_summary()
            self.workflow_state["summary"] = final_summary
            
            logger.info(f"Workflow {workflow_id} completed successfully")
            return self.workflow_state
            
        except Exception as e:
            logger.error(f"Workflow {workflow_id} failed: {e}")
            
            self.workflow_state["status"] = "failed"
            self.workflow_state["end_time"] = datetime.now()
            self.workflow_state["errors"].append({
                "phase": self.workflow_state.get("current_phase", "unknown"),
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            raise
    
    async def _execute_planning_phase(
        self,
        manufacturers: List[str],
        priorities: Optional[Dict[str, int]]
    ) -> Dict[str, Any]:
        """Execute the planning phase."""
        
        logger.info("Executing planning phase")
        
        planning_input = {
            "manufacturers": manufacturers,
            "priorities": priorities or {mfg: 3 for mfg in manufacturers}
        }
        
        planning_results = await self.planner.execute(planning_input)
        
        # Validate planning results
        validation = await self.planner.validate_plan(planning_results)
        if not validation.get("valid", False):
            raise ValueError(f"Invalid planning results: {validation.get('errors', [])}")
        
        logger.info("Planning phase completed successfully")
        return planning_results
    
    async def _execute_crawling_phase(self, planning_results: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the crawling phase."""
        
        logger.info("Executing crawling phase")
        
        crawling_input = {
            "plan": planning_results,
            "manufacturers": planning_results.get("manufacturers", [])
        }
        
        crawling_results = await self.crawler.execute(crawling_input)
        
        # Check crawling success rate
        summary = crawling_results.get("execution_summary", {})
        success_rate = summary.get("successful_crawls", 0) / max(1, summary.get("total_manufacturers", 1))
        
        if success_rate < 0.5:
            logger.warning(f"Low crawling success rate: {success_rate:.2%}")
        
        logger.info("Crawling phase completed successfully")
        return crawling_results
    
    async def _execute_extraction_phase(self, crawling_results: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the extraction phase."""
        
        logger.info("Executing extraction phase")
        
        extraction_input = {
            "raw_data": crawling_results.get("manufacturer_results", []),
            "config": {
                "max_content_length": 8000,
                "confidence_threshold": 0.3
            }
        }
        
        extraction_results = await self.extractor.execute(extraction_input)
        
        # Check extraction quality
        quality_metrics = extraction_results.get("quality_metrics", {})
        if quality_metrics.get("average_confidence", 0) < 0.5:
            logger.warning(f"Low extraction confidence: {quality_metrics.get('average_confidence', 0):.3f}")
        
        logger.info("Extraction phase completed successfully")
        return extraction_results
    
    async def _execute_validation_phase(self, extraction_results: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the validation phase."""
        
        logger.info("Executing validation phase")
        
        validation_input = {
            "extracted_equipment": extraction_results.get("extracted_equipment", []),
            "quality_metrics": extraction_results.get("quality_metrics", {}),
            "validation_rules": {
                "min_confidence": 0.3,
                "required_fields": ["manufacturer", "model", "type"],
                "duplicate_check": True
            }
        }
        
        validation_results = await self.validator.execute(validation_input)
        
        logger.info("Validation phase completed successfully")
        return validation_results
    
    async def _execute_integration_phase(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the integration phase."""
        
        logger.info("Executing integration phase")
        
        integration_input = {
            "validated_equipment": validation_results.get("validated_equipment", []),
            "validation_report": validation_results.get("validation_report", {}),
            "integration_config": {
                "update_existing": True,
                "duplicate_handling": "merge",
                "batch_size": 50
            }
        }
        
        integration_results = await self.integrator.execute(integration_input)
        
        logger.info("Integration phase completed successfully")
        return integration_results
    
    async def _generate_workflow_summary(self) -> Dict[str, Any]:
        """Generate comprehensive workflow summary."""
        
        start_time = self.workflow_state.get("start_time")
        end_time = self.workflow_state.get("end_time")
        duration = (end_time - start_time).total_seconds() if start_time and end_time else 0
        
        # Extract key metrics from each phase
        planning_results = self.workflow_state["results"].get("planning", {})
        crawling_results = self.workflow_state["results"].get("crawling", {})
        extraction_results = self.workflow_state["results"].get("extraction", {})
        validation_results = self.workflow_state["results"].get("validation", {})
        integration_results = self.workflow_state["results"].get("integration", {})
        
        summary = {
            "workflow_id": self.workflow_state.get("id"),
            "status": self.workflow_state.get("status"),
            "duration_seconds": duration,
            "duration_formatted": f"{duration//3600:.0f}h {(duration%3600)//60:.0f}m {duration%60:.0f}s",
            
            # Planning metrics
            "manufacturers_planned": len(planning_results.get("manufacturers", [])),
            "total_estimated_duration": planning_results.get("total_estimated_duration", "Unknown"),
            
            # Crawling metrics
            "pages_crawled": crawling_results.get("execution_summary", {}).get("total_pages_crawled", 0),
            "crawling_success_rate": (
                crawling_results.get("execution_summary", {}).get("successful_crawls", 0) /
                max(1, crawling_results.get("execution_summary", {}).get("total_manufacturers", 1))
            ),
            
            # Extraction metrics
            "equipment_extracted": extraction_results.get("extraction_summary", {}).get("equipment_extracted", 0),
            "extraction_confidence": extraction_results.get("quality_metrics", {}).get("average_confidence", 0),
            
            # Validation metrics
            "equipment_validated": len(validation_results.get("validated_equipment", [])),
            "validation_issues": len(validation_results.get("validation_report", {}).get("issues", [])),
            
            # Integration metrics
            "equipment_saved": integration_results.get("database_results", {}).get("saved", 0),
            "equipment_updated": integration_results.get("database_results", {}).get("updated", 0),
            "integration_errors": integration_results.get("database_results", {}).get("errors", 0),
            
            # Overall success metrics
            "total_errors": len(self.workflow_state.get("errors", [])),
            "success": self.workflow_state.get("status") == "completed"
        }
        
        return summary
    
    async def _perform_health_checks(self) -> Dict[str, Dict[str, Any]]:
        """Perform health checks on all agents."""
        
        health_checks = {}
        
        agents = {
            "planner": self.planner,
            "crawler": self.crawler,
            "extractor": self.extractor,
            "integrator": self.integrator,
            "validator": self.validator
        }
        
        for agent_name, agent in agents.items():
            try:
                health_checks[agent_name] = await agent.health_check()
            except Exception as e:
                health_checks[agent_name] = {
                    "healthy": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
        
        # Check database manager
        try:
            health_checks["database"] = await self.database_manager.health_check()
        except Exception as e:
            health_checks["database"] = {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        
        return health_checks
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """Get current workflow status."""
        return self.workflow_state.copy()
    
    async def cancel_workflow(self) -> Dict[str, Any]:
        """Cancel the current workflow."""
        
        if self.workflow_state.get("status") == "running":
            self.workflow_state["status"] = "cancelled"
            self.workflow_state["end_time"] = datetime.now()
            
            logger.info(f"Workflow {self.workflow_state.get('id')} cancelled")
        
        return self.workflow_state
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        
        health_checks = await self._perform_health_checks()
        
        # Get database statistics
        db_stats = {}
        try:
            db_stats = await self.database_manager.get_statistics()
        except Exception as e:
            logger.error(f"Failed to get database statistics: {e}")
        
        return {
            "system": "Krabulon HVAC Data Enrichment System",
            "version": "1.0.0",
            "status": "operational" if all(h.get("healthy", False) for h in health_checks.values()) else "degraded",
            "timestamp": datetime.now().isoformat(),
            "components": health_checks,
            "database_statistics": db_stats,
            "current_workflow": self.workflow_state if self.workflow_state.get("status") == "running" else None
        }
