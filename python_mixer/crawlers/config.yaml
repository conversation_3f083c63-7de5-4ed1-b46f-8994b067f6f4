# Krabulon Configuration
app:
  name: "Krabulon HVAC Data Enrichment System"
  version: "1.0.0"
  debug: false
  host: "0.0.0.0"
  port: 8000

# Database configurations
databases:
  postgresql:
    host: "localhost"
    port: 5432
    database: "hvac_crm"
    username: "postgres"
    password: "password"
    pool_size: 10
    max_overflow: 20
    
  mongodb:
    host: "localhost"
    port: 27017
    database: "hvac_equipment"
    username: ""
    password: ""
    
  neo4j:
    uri: "bolt://localhost:7687"
    username: "neo4j"
    password: "password"
    
  redis:
    host: "localhost"
    port: 6379
    database: 0
    password: ""

# AI/LLM Configuration
ai:
  openai:
    api_key: "${OPENAI_API_KEY}"
    model: "gpt-4-turbo-preview"
    temperature: 0.1
    max_tokens: 4000
    
  crewai:
    verbose: true
    memory: true
    
# Crawling Configuration
crawling:
  crawl4ai:
    max_pages: 50
    delay: 1.0
    timeout: 30
    user_agent: "Krabulon HVAC Bot 1.0"
    respect_robots_txt: true
    
  playwright:
    headless: true
    timeout: 30000
    
# HVAC Manufacturers to crawl
manufacturers:
  - name: "LG"
    base_url: "https://www.lg.com"
    product_pages:
      - "/hvac/air-conditioners"
      - "/hvac/heat-pumps"
    selectors:
      product_list: ".product-grid .product-item"
      product_specs: ".specifications-table"
      
  - name: "Daikin"
    base_url: "https://www.daikin.com"
    product_pages:
      - "/products/air-conditioners"
      - "/products/heat-pumps"
    selectors:
      product_list: ".product-listing .product"
      product_specs: ".tech-specs"
      
  - name: "Mitsubishi"
    base_url: "https://www.mitsubishielectric.com"
    product_pages:
      - "/hvac/products"
    selectors:
      product_list: ".products-grid .product-card"
      product_specs: ".specifications"

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "logs/krabulon.log"
  rotation: "1 day"
  retention: "30 days"

# API Configuration
api:
  title: "Krabulon HVAC API"
  description: "AI-powered HVAC equipment data enrichment system"
  version: "1.0.0"
  docs_url: "/docs"
  redoc_url: "/redoc"
  
# Security
security:
  secret_key: "${SECRET_KEY}"
  algorithm: "HS256"
  access_token_expire_minutes: 30

# Monitoring
monitoring:
  prometheus:
    enabled: true
    port: 9090
  health_check:
    enabled: true
    interval: 60

# Task Configuration
tasks:
  crawling:
    schedule: "0 2 * * *"  # Daily at 2 AM
    batch_size: 10
    retry_attempts: 3
    
  validation:
    confidence_threshold: 0.8
    required_fields: ["manufacturer", "model", "type"]
    
  integration:
    duplicate_check: true
    update_existing: true
