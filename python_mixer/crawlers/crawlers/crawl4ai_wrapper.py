
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Enhanced Crawl4AI wrapper for HVAC equipment data extraction with 2024 features."""

import asyncio
import json
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pathlib import Path
from loguru import logger

try:
    from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode, LLMConfig
    from crawl4ai.extraction_strategy import JsonCssExtractionStrategy, LLMExtractionStrategy
    from crawl4ai.content_filter_strategy import Pruning<PERSON>ontentFilter, BM25ContentFilter
    from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
    CRAWL4AI_AVAILABLE = True
except ImportError:
    logger.warning("Crawl4AI not available, using fallback crawler")
    CRAWL4AI_AVAILABLE = False

from config import settings


class Crawl4AIWrapper:
    """Enhanced Crawl4AI wrapper with HVAC-specific optimizations and 2024 features."""

    def __init__(self, enable_stealth: bool = True, enable_image_extraction: bool = True):
        self.crawler = None
        self.session_active = False
        self.enable_stealth = enable_stealth
        self.enable_image_extraction = enable_image_extraction
        self.hvac_manufacturers = [
            "lg", "daikin", "carrier", "trane", "york", "lennox", "rheem", "goodman",
            "american-standard", "bryant", "payne", "heil", "tempstar", "comfortmaker",
            "mitsubishi", "fujitsu", "panasonic", "samsung", "gree", "midea"
        ]
        self.hvac_equipment_schema = self._create_hvac_schema()
        self.image_storage_path = Path("data/hvac_images")
        self.image_storage_path.mkdir(parents=True, exist_ok=True)

    def _create_hvac_schema(self) -> Dict[str, Any]:
        """Create comprehensive HVAC equipment extraction schema."""
        return {
            "type": "object",
            "properties": {
                "equipment_data": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "model_number": {"type": "string", "description": "Equipment model number"},
                            "product_name": {"type": "string", "description": "Product name or title"},
                            "manufacturer": {"type": "string", "description": "Manufacturer name"},
                            "equipment_type": {"type": "string", "description": "Type of HVAC equipment (AC, heat pump, furnace, etc.)"},
                            "capacity_btu": {"type": "string", "description": "BTU capacity rating"},
                            "capacity_kw": {"type": "string", "description": "kW capacity rating"},
                            "energy_efficiency": {"type": "string", "description": "SEER, EER, or other efficiency ratings"},
                            "refrigerant_type": {"type": "string", "description": "Refrigerant type (R-410A, R-32, etc.)"},
                            "dimensions": {
                                "type": "object",
                                "properties": {
                                    "width": {"type": "string"},
                                    "height": {"type": "string"},
                                    "depth": {"type": "string"},
                                    "weight": {"type": "string"}
                                }
                            },
                            "electrical_specs": {
                                "type": "object",
                                "properties": {
                                    "voltage": {"type": "string"},
                                    "phase": {"type": "string"},
                                    "amperage": {"type": "string"},
                                    "power_consumption": {"type": "string"}
                                }
                            },
                            "operating_conditions": {
                                "type": "object",
                                "properties": {
                                    "min_temp": {"type": "string"},
                                    "max_temp": {"type": "string"},
                                    "humidity_range": {"type": "string"}
                                }
                            },
                            "features": {"type": "array", "items": {"type": "string"}},
                            "certifications": {"type": "array", "items": {"type": "string"}},
                            "warranty": {"type": "string", "description": "Warranty information"},
                            "price": {"type": "string", "description": "Price if available"},
                            "availability": {"type": "string", "description": "Availability status"},
                            "product_url": {"type": "string", "description": "Direct product page URL"},
                            "image_urls": {"type": "array", "items": {"type": "string"}},
                            "datasheet_url": {"type": "string", "description": "Technical datasheet URL"},
                            "manual_url": {"type": "string", "description": "Installation/operation manual URL"}
                        }
                    }
                }
            }
        }

    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
    
    async def initialize(self):
        """Initialize the enhanced crawler with modern features."""
        if not CRAWL4AI_AVAILABLE:
            raise RuntimeError("Crawl4AI is not available")

        try:
            # Create enhanced browser configuration
            browser_config = BrowserConfig(
                headless=settings.crawling.playwright_headless,
                browser_type="chromium",
                user_agent=settings.crawling.user_agent,
                verbose=True,
                # Enable stealth mode for better bot detection avoidance
                use_stealth=self.enable_stealth,
                # Enable image processing
                accept_downloads=True,
                # Set viewport for consistent rendering
                viewport_width=1920,
                viewport_height=1080,
                # Enable network capture for debugging
                capture_network=True,
                # Enable console capture for debugging
                capture_console=True,
                # Set user data directory for session persistence
                user_data_dir=str(Path.home() / ".crawl4ai" / "hvac_crawler"),
                # Enable persistent context
                use_persistent_context=True
            )

            self.crawler = AsyncWebCrawler(config=browser_config)
            await self.crawler.start()
            self.session_active = True
            logger.info("Enhanced Crawl4AI wrapper initialized successfully with stealth mode and image extraction")

        except Exception as e:
            logger.error(f"Failed to initialize enhanced Crawl4AI: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup enhanced crawler resources."""
        if self.crawler and self.session_active:
            try:
                await self.crawler.stop()
                self.session_active = False
                logger.info("Enhanced Crawl4AI wrapper cleaned up")
            except Exception as e:
                logger.error(f"Error during enhanced crawler cleanup: {e}")

    async def crawl_hvac_equipment(
        self,
        url: str,
        extraction_mode: str = "structured",  # "structured", "llm", "hybrid"
        save_images: bool = True,
        extract_tables: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Enhanced HVAC equipment crawling with modern extraction techniques.

        Args:
            url: URL to crawl
            extraction_mode: Type of extraction ("structured", "llm", "hybrid")
            save_images: Whether to download and save equipment images
            extract_tables: Whether to extract specification tables
            **kwargs: Additional crawling parameters

        Returns:
            Comprehensive crawling result with HVAC-specific data
        """
        if not self.session_active:
            raise RuntimeError("Crawler not initialized")

        start_time = datetime.now()

        try:
            logger.info(f"Starting enhanced HVAC crawl for URL: {url}")

            # Create enhanced crawl configuration
            crawl_config = self._create_hvac_crawl_config(
                extraction_mode=extraction_mode,
                extract_tables=extract_tables,
                **kwargs
            )

            # Execute crawling with enhanced configuration
            result = await self.crawler.arun(url=url, config=crawl_config)

            # Process and enhance the result
            enhanced_result = await self._process_hvac_result(
                result, url, start_time, save_images, extract_tables
            )

            logger.info(f"Enhanced HVAC crawl completed for {url}: {len(enhanced_result.get('content', ''))} chars")
            return enhanced_result

        except Exception as e:
            logger.error(f"Enhanced HVAC crawl failed for {url}: {e}")
            return {
                "url": url,
                "success": False,
                "error": str(e),
                "content": "",
                "equipment_data": [],
                "images": [],
                "tables": [],
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration_seconds": (datetime.now() - start_time).total_seconds(),
                    "extraction_mode": extraction_mode
                }
            }

    async def crawl_url(
        self,
        url: str,
        max_pages: int = 20,
        delay: float = 1.0,
        timeout: int = 30,
        selectors: Optional[Dict[str, str]] = None,
        deep_crawl: str = "bfs",
        content_filter: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Crawl a single URL with HVAC-specific optimizations.
        
        Args:
            url: URL to crawl
            max_pages: Maximum pages to crawl
            delay: Delay between requests
            timeout: Request timeout
            selectors: CSS selectors for content extraction
            deep_crawl: Deep crawling strategy (bfs/dfs/false)
            content_filter: Content filtering rules
            **kwargs: Additional crawling parameters
            
        Returns:
            Crawling result with extracted content
        """
        if not self.session_active:
            raise RuntimeError("Crawler not initialized")
        
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting crawl for URL: {url}")
            
            # Prepare crawling parameters
            crawl_params = self._prepare_crawl_params(
                url=url,
                max_pages=max_pages,
                delay=delay,
                timeout=timeout,
                selectors=selectors,
                deep_crawl=deep_crawl,
                content_filter=content_filter,
                **kwargs
            )
            
            # Execute crawling
            result = await self.crawler.arun(**crawl_params)
            
            # Process result
            processed_result = self._process_crawl_result(result, url, start_time)
            
            logger.info(f"Crawl completed for {url}: {len(processed_result.get('content', ''))} chars")
            return processed_result
            
        except Exception as e:
            logger.error(f"Crawl failed for {url}: {e}")
            return {
                "url": url,
                "success": False,
                "error": str(e),
                "content": "",
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration_seconds": (datetime.now() - start_time).total_seconds()
                }
            }
    
    async def crawl_urls(
        self,
        urls: List[str],
        max_pages: int = 20,
        delay: float = 1.0,
        timeout: int = 30,
        selectors: Optional[Dict[str, str]] = None,
        deep_crawl: str = "bfs",
        content_filter: Optional[List[str]] = None,
        parallel: bool = False,
        max_concurrent: int = 3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl multiple URLs.
        
        Args:
            urls: List of URLs to crawl
            max_pages: Maximum pages per URL
            delay: Delay between requests
            timeout: Request timeout
            selectors: CSS selectors for content extraction
            deep_crawl: Deep crawling strategy
            content_filter: Content filtering rules
            parallel: Enable parallel crawling
            max_concurrent: Maximum concurrent crawls
            **kwargs: Additional crawling parameters
            
        Returns:
            List of crawling results
        """
        if not urls:
            return []
        
        logger.info(f"Starting crawl for {len(urls)} URLs (parallel: {parallel})")
        
        if parallel and len(urls) > 1:
            return await self._crawl_urls_parallel(
                urls, max_concurrent, max_pages, delay, timeout,
                selectors, deep_crawl, content_filter, **kwargs
            )
        else:
            return await self._crawl_urls_sequential(
                urls, max_pages, delay, timeout,
                selectors, deep_crawl, content_filter, **kwargs
            )
    
    async def _crawl_urls_sequential(
        self,
        urls: List[str],
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        deep_crawl: str,
        content_filter: Optional[List[str]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl URLs sequentially."""
        results = []
        
        for i, url in enumerate(urls):
            try:
                result = await self.crawl_url(
                    url=url,
                    max_pages=max_pages,
                    delay=delay,
                    timeout=timeout,
                    selectors=selectors,
                    deep_crawl=deep_crawl,
                    content_filter=content_filter,
                    **kwargs
                )
                results.append(result)
                
                # Add delay between URLs
                if i < len(urls) - 1:
                    await asyncio.sleep(delay)
                    
            except Exception as e:
                logger.error(f"Failed to crawl {url}: {e}")
                results.append({
                    "url": url,
                    "success": False,
                    "error": str(e),
                    "content": "",
                    "metadata": {}
                })
        
        return results
    
    async def _crawl_urls_parallel(
        self,
        urls: List[str],
        max_concurrent: int,
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        deep_crawl: str,
        content_filter: Optional[List[str]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl URLs in parallel with concurrency limit."""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.crawl_url(
                    url=url,
                    max_pages=max_pages,
                    delay=delay,
                    timeout=timeout,
                    selectors=selectors,
                    deep_crawl=deep_crawl,
                    content_filter=content_filter,
                    **kwargs
                )
        
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "url": urls[i],
                    "success": False,
                    "error": str(result),
                    "content": "",
                    "metadata": {}
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _prepare_crawl_params(
        self,
        url: str,
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        deep_crawl: str,
        content_filter: Optional[List[str]],
        **kwargs
    ) -> Dict[str, Any]:
        """Prepare crawling parameters for Crawl4AI."""
        
        params = {
            "url": url,
            "word_count_threshold": 50,
            "fit_markdown": True,
            "bypass_cache": False,
            "include_raw_html": False,
            "wait_for": 2000,  # Wait for dynamic content
        }
        
        # Deep crawling configuration
        if deep_crawl and deep_crawl != "false":
            params.update({
                "crawler_strategy": deep_crawl,
                "max_depth": 2,
                "max_pages": min(max_pages, 50)
            })
        
        # Content selectors
        if selectors:
            content_selectors = []
            for selector_type, selector in selectors.items():
                if selector:
                    content_selectors.append(selector)
            
            if content_selectors:
                params["css_selector"] = ", ".join(content_selectors)
        
        # HVAC-specific content filtering
        hvac_keywords = [
            "air conditioner", "heat pump", "hvac", "cooling", "heating",
            "refrigerant", "btu", "kw", "energy efficiency", "specifications",
            "technical data", "product details", "model", "series"
        ]
        
        params["extraction_strategy"] = "LLMExtractionStrategy"
        params["extraction_schema"] = {
            "type": "object",
            "properties": {
                "equipment_data": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "model": {"type": "string"},
                            "type": {"type": "string"},
                            "specifications": {"type": "object"}
                        }
                    }
                }
            }
        }
        
        # Add custom parameters
        params.update(kwargs)
        
        return params

    def _create_hvac_crawl_config(
        self,
        extraction_mode: str = "structured",
        extract_tables: bool = True,
        **kwargs
    ) -> CrawlerRunConfig:
        """Create enhanced crawl configuration for HVAC equipment."""

        # Base configuration
        config_params = {
            "cache_mode": CacheMode.ENABLED,
            "word_count_threshold": 50,
            "fit_markdown": True,
            "include_raw_html": True,
            "wait_for": 3000,  # Wait for dynamic content
            "js_code": [
                # Scroll to load lazy images
                "window.scrollTo(0, document.body.scrollHeight);",
                # Wait for images to load
                "await new Promise(resolve => setTimeout(resolve, 2000));",
                # Click any "show more" or "specifications" buttons
                """
                const buttons = document.querySelectorAll('button, a');
                for (let btn of buttons) {
                    const text = btn.textContent.toLowerCase();
                    if (text.includes('specification') || text.includes('details') ||
                        text.includes('more') || text.includes('tech')) {
                        try { btn.click(); await new Promise(r => setTimeout(r, 1000)); } catch(e) {}
                    }
                }
                """
            ]
        }

        # Add table extraction if requested
        if extract_tables:
            config_params["table_score_threshold"] = 8

        # Configure extraction strategy based on mode
        if extraction_mode == "structured":
            # Use CSS-based extraction for structured data
            config_params["extraction_strategy"] = JsonCssExtractionStrategy(
                schema=self._create_hvac_css_schema(),
                verbose=True
            )
        elif extraction_mode == "llm":
            # Use LLM-based extraction for complex data
            config_params["extraction_strategy"] = LLMExtractionStrategy(
                llm_config=LLMConfig(
                    provider="openai/gpt-4o-mini",  # Use efficient model
                    api_token=kwargs.get("openai_api_key", ""),
                    temperature=0.1
                ),
                schema=self.hvac_equipment_schema,
                extraction_type="schema",
                instruction=self._get_hvac_extraction_instruction()
            )
        elif extraction_mode == "hybrid":
            # Use both strategies for maximum coverage
            config_params["extraction_strategy"] = JsonCssExtractionStrategy(
                schema=self._create_hvac_css_schema(),
                verbose=True
            )

        # Add content filtering for better results
        config_params["markdown_generator"] = DefaultMarkdownGenerator(
            content_filter=BM25ContentFilter(
                user_query="HVAC equipment specifications technical data BTU SEER energy efficiency",
                bm25_threshold=1.0
            )
        )

        return CrawlerRunConfig(**config_params)

    def _create_hvac_css_schema(self) -> Dict[str, Any]:
        """Create CSS-based extraction schema for HVAC equipment."""
        return {
            "name": "HVAC Equipment Data",
            "baseSelector": "body",
            "fields": [
                {
                    "name": "model_number",
                    "selector": "[class*='model'], [class*='part'], [id*='model'], [data-model]",
                    "type": "text"
                },
                {
                    "name": "product_name",
                    "selector": "h1, h2, [class*='title'], [class*='name'], [class*='product']",
                    "type": "text"
                },
                {
                    "name": "capacity_btu",
                    "selector": "[class*='btu'], [class*='capacity'], [data-btu]",
                    "type": "text"
                },
                {
                    "name": "energy_efficiency",
                    "selector": "[class*='seer'], [class*='eer'], [class*='efficiency']",
                    "type": "text"
                },
                {
                    "name": "price",
                    "selector": "[class*='price'], [class*='cost'], [data-price]",
                    "type": "text"
                },
                {
                    "name": "specifications",
                    "selector": "[class*='spec'], [class*='technical'], table",
                    "type": "text"
                },
                {
                    "name": "images",
                    "selector": "img[src*='product'], img[src*='equipment'], img[alt*='hvac']",
                    "type": "attribute",
                    "attribute": "src"
                },
                {
                    "name": "features",
                    "selector": "[class*='feature'], [class*='benefit'], ul li, ol li",
                    "type": "text"
                }
            ]
        }

    def _get_hvac_extraction_instruction(self) -> str:
        """Get detailed instruction for LLM-based HVAC extraction."""
        return """
        Extract comprehensive HVAC equipment information from the webpage content.
        Focus on technical specifications, model numbers, capacity ratings, and energy efficiency data.

        Key information to extract:
        1. Model numbers and product names
        2. BTU capacity and kW ratings
        3. Energy efficiency ratings (SEER, EER, COP, etc.)
        4. Refrigerant type (R-410A, R-32, etc.)
        5. Physical dimensions and weight
        6. Electrical specifications (voltage, amperage, phase)
        7. Operating temperature ranges
        8. Features and certifications
        9. Warranty information
        10. Pricing if available
        11. Product images and documentation URLs

        Return structured JSON data following the provided schema.
        If multiple products are found, include all of them in the equipment_data array.
        """

    async def _process_hvac_result(
        self,
        result: Any,
        url: str,
        start_time: datetime,
        save_images: bool = True,
        extract_tables: bool = True
    ) -> Dict[str, Any]:
        """Process and enhance HVAC crawling result with modern features."""

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        if hasattr(result, 'success') and result.success:
            content = getattr(result, 'markdown', '') or getattr(result, 'cleaned_html', '')

            # Extract structured data
            equipment_data = []
            if hasattr(result, 'extracted_content') and result.extracted_content:
                try:
                    extracted = json.loads(result.extracted_content)
                    if isinstance(extracted, dict) and 'equipment_data' in extracted:
                        equipment_data = extracted['equipment_data']
                    elif isinstance(extracted, list):
                        equipment_data = extracted
                except json.JSONDecodeError:
                    logger.warning("Failed to parse extracted content as JSON")

            # Process images
            images = []
            if hasattr(result, 'media') and result.media and 'images' in result.media:
                images = result.media['images']
                if save_images and self.enable_image_extraction:
                    await self._download_hvac_images(images, url)

            # Process tables
            tables = []
            if extract_tables and hasattr(result, 'media') and result.media and 'tables' in result.media:
                tables = result.media['tables']
                # Convert tables to DataFrame format for easier processing
                for i, table in enumerate(tables):
                    if 'rows' in table and 'headers' in table:
                        try:
                            df = pd.DataFrame(table['rows'], columns=table['headers'])
                            table['dataframe_info'] = {
                                'shape': df.shape,
                                'columns': df.columns.tolist(),
                                'sample_data': df.head(3).to_dict('records') if not df.empty else []
                            }
                        except Exception as e:
                            logger.warning(f"Failed to process table {i}: {e}")

            return {
                "url": url,
                "success": True,
                "content": content,
                "equipment_data": equipment_data,
                "images": images,
                "tables": tables,
                "raw_html": getattr(result, 'html', ''),
                "links": getattr(result, 'links', []),
                "network_logs": getattr(result, 'network_logs', []) if hasattr(result, 'network_logs') else [],
                "console_logs": getattr(result, 'console_logs', []) if hasattr(result, 'console_logs') else [],
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "content_length": len(content),
                    "equipment_count": len(equipment_data),
                    "image_count": len(images),
                    "table_count": len(tables),
                    "status_code": getattr(result, 'status_code', 200),
                    "response_headers": getattr(result, 'response_headers', {}),
                    "crawler_version": "enhanced_v2024"
                }
            }
        else:
            error_msg = getattr(result, 'error_message', 'Unknown crawling error')

            return {
                "url": url,
                "success": False,
                "error": error_msg,
                "content": "",
                "equipment_data": [],
                "images": [],
                "tables": [],
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "status_code": getattr(result, 'status_code', 0),
                    "crawler_version": "enhanced_v2024"
                }
            }

    async def _download_hvac_images(self, images: List[Dict], source_url: str) -> List[str]:
        """Download HVAC equipment images for future use."""
        downloaded_paths = []

        try:
            import aiohttp
            import aiofiles
            from urllib.parse import urljoin, urlparse

            async with aiohttp.ClientSession() as session:
                for i, image_info in enumerate(images[:10]):  # Limit to 10 images
                    try:
                        image_url = image_info.get('src', '') if isinstance(image_info, dict) else str(image_info)
                        if not image_url:
                            continue

                        # Make URL absolute
                        if not image_url.startswith(('http://', 'https://')):
                            image_url = urljoin(source_url, image_url)

                        # Generate filename
                        parsed_url = urlparse(image_url)
                        filename = f"hvac_equipment_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i}.jpg"
                        filepath = self.image_storage_path / filename

                        # Download image
                        async with session.get(image_url) as response:
                            if response.status == 200:
                                async with aiofiles.open(filepath, 'wb') as f:
                                    async for chunk in response.content.iter_chunked(8192):
                                        await f.write(chunk)

                                downloaded_paths.append(str(filepath))
                                logger.info(f"Downloaded HVAC image: {filename}")

                    except Exception as e:
                        logger.warning(f"Failed to download image {i}: {e}")
                        continue

        except ImportError:
            logger.warning("aiohttp or aiofiles not available for image downloading")
        except Exception as e:
            logger.error(f"Error downloading HVAC images: {e}")

        return downloaded_paths

    def _process_crawl_result(
        self,
        result: Any,
        url: str,
        start_time: datetime
    ) -> Dict[str, Any]:
        """Process and normalize crawl result."""
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if hasattr(result, 'success') and result.success:
            content = getattr(result, 'markdown', '') or getattr(result, 'cleaned_html', '')
            
            return {
                "url": url,
                "success": True,
                "content": content,
                "raw_html": getattr(result, 'html', ''),
                "links": getattr(result, 'links', []),
                "images": getattr(result, 'images', []),
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "content_length": len(content),
                    "status_code": getattr(result, 'status_code', 200),
                    "response_headers": getattr(result, 'response_headers', {}),
                    "extracted_content": getattr(result, 'extracted_content', None)
                }
            }
        else:
            error_msg = getattr(result, 'error_message', 'Unknown crawling error')
            
            return {
                "url": url,
                "success": False,
                "error": error_msg,
                "content": "",
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "status_code": getattr(result, 'status_code', 0)
                }
            }
