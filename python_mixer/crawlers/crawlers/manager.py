
import sys
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

"""Crawler Manager for coordinating web crawling operations."""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .crawl4ai_wrapper import <PERSON>raw<PERSON><PERSON><PERSON><PERSON><PERSON>rap<PERSON>, CRAWL4AI_AVAILABLE
from config import settings


class CrawlerManager:
    """Manager for coordinating web crawling operations."""
    
    def __init__(self):
        self.active_crawlers = {}
        self.crawl_stats = {
            "total_crawls": 0,
            "successful_crawls": 0,
            "failed_crawls": 0,
            "total_pages": 0,
            "total_duration": 0
        }
    
    async def crawl_urls(
        self,
        urls: List[str],
        max_pages: int = 20,
        delay: float = 1.0,
        timeout: int = 30,
        selectors: Optional[Dict[str, str]] = None,
        deep_crawl: str = "bfs",
        content_filter: Optional[List[str]] = None,
        parallel: bool = False,
        max_concurrent: int = 3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl multiple URLs using the best available crawler.
        
        Args:
            urls: List of URLs to crawl
            max_pages: Maximum pages per URL
            delay: Delay between requests
            timeout: Request timeout
            selectors: CSS selectors for content extraction
            deep_crawl: Deep crawling strategy
            content_filter: Content filtering rules
            parallel: Enable parallel crawling
            max_concurrent: Maximum concurrent crawls
            **kwargs: Additional crawling parameters
            
        Returns:
            List of crawling results
        """
        if not urls:
            return []
        
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting crawl operation for {len(urls)} URLs")
            
            # Choose crawler implementation
            if CRAWL4AI_AVAILABLE:
                results = await self._crawl_with_crawl4ai(
                    urls, max_pages, delay, timeout, selectors,
                    deep_crawl, content_filter, parallel, max_concurrent, **kwargs
                )
            else:
                results = await self._crawl_with_fallback(
                    urls, max_pages, delay, timeout, selectors, **kwargs
                )
            
            # Update statistics
            duration = (datetime.now() - start_time).total_seconds()
            self._update_stats(results, duration)
            
            logger.info(f"Crawl operation completed: {len(results)} results in {duration:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"Crawl operation failed: {e}")
            raise
    
    async def _crawl_with_crawl4ai(
        self,
        urls: List[str],
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        deep_crawl: str,
        content_filter: Optional[List[str]],
        parallel: bool,
        max_concurrent: int,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl URLs using Crawl4AI."""
        
        async with Crawl4AIWrapper() as crawler:
            return await crawler.crawl_urls(
                urls=urls,
                max_pages=max_pages,
                delay=delay,
                timeout=timeout,
                selectors=selectors,
                deep_crawl=deep_crawl,
                content_filter=content_filter,
                parallel=parallel,
                max_concurrent=max_concurrent,
                **kwargs
            )
    
    async def _crawl_with_fallback(
        self,
        urls: List[str],
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Fallback crawler using requests and BeautifulSoup."""
        
        import aiohttp
        from bs4 import BeautifulSoup
        
        results = []
        
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=timeout),
            headers={"User-Agent": settings.crawling.user_agent}
        ) as session:
            
            for url in urls:
                start_time = datetime.now()
                
                try:
                    logger.info(f"Crawling {url} with fallback crawler")
                    
                    async with session.get(url) as response:
                        if response.status == 200:
                            html_content = await response.text()
                            
                            # Parse with BeautifulSoup
                            soup = BeautifulSoup(html_content, 'html.parser')
                            
                            # Extract content based on selectors
                            content = self._extract_content_with_selectors(soup, selectors)
                            
                            # Convert to markdown-like format
                            markdown_content = self._html_to_markdown(content)
                            
                            results.append({
                                "url": url,
                                "success": True,
                                "content": markdown_content,
                                "raw_html": html_content,
                                "metadata": {
                                    "start_time": start_time.isoformat(),
                                    "end_time": datetime.now().isoformat(),
                                    "duration_seconds": (datetime.now() - start_time).total_seconds(),
                                    "content_length": len(markdown_content),
                                    "status_code": response.status,
                                    "crawler": "fallback"
                                }
                            })
                        else:
                            results.append({
                                "url": url,
                                "success": False,
                                "error": f"HTTP {response.status}",
                                "content": "",
                                "metadata": {
                                    "start_time": start_time.isoformat(),
                                    "end_time": datetime.now().isoformat(),
                                    "duration_seconds": (datetime.now() - start_time).total_seconds(),
                                    "status_code": response.status,
                                    "crawler": "fallback"
                                }
                            })
                
                except Exception as e:
                    logger.error(f"Fallback crawl failed for {url}: {e}")
                    results.append({
                        "url": url,
                        "success": False,
                        "error": str(e),
                        "content": "",
                        "metadata": {
                            "start_time": start_time.isoformat(),
                            "end_time": datetime.now().isoformat(),
                            "duration_seconds": (datetime.now() - start_time).total_seconds(),
                            "crawler": "fallback"
                        }
                    })
                
                # Add delay between requests
                if delay > 0:
                    await asyncio.sleep(delay)
        
        return results
    
    def _extract_content_with_selectors(
        self,
        soup: Any,
        selectors: Optional[Dict[str, str]]
    ) -> str:
        """Extract content using CSS selectors."""
        
        if not selectors:
            # Default extraction - get main content
            content_tags = soup.find_all(['main', 'article', 'section', 'div'])
            content = ' '.join([tag.get_text(strip=True) for tag in content_tags])
        else:
            content_parts = []
            
            for selector_name, selector in selectors.items():
                try:
                    elements = soup.select(selector)
                    for element in elements:
                        text = element.get_text(strip=True)
                        if text:
                            content_parts.append(f"[{selector_name}] {text}")
                except Exception as e:
                    logger.warning(f"Failed to apply selector '{selector}': {e}")
            
            content = '\n\n'.join(content_parts)
        
        return content or soup.get_text(strip=True)
    
    def _html_to_markdown(self, content: str) -> str:
        """Convert HTML content to markdown-like format."""
        
        # Simple HTML to markdown conversion
        import re
        
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Add line breaks for better readability
        content = re.sub(r'(\. )([A-Z])', r'\1\n\n\2', content)
        
        return content.strip()
    
    def _update_stats(self, results: List[Dict[str, Any]], duration: float):
        """Update crawling statistics."""
        
        self.crawl_stats["total_crawls"] += len(results)
        self.crawl_stats["successful_crawls"] += len([r for r in results if r.get("success")])
        self.crawl_stats["failed_crawls"] += len([r for r in results if not r.get("success")])
        self.crawl_stats["total_duration"] += duration
        
        # Estimate pages crawled (simplified)
        self.crawl_stats["total_pages"] += len(results)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get crawling statistics."""
        
        stats = self.crawl_stats.copy()
        
        if stats["total_crawls"] > 0:
            stats["success_rate"] = stats["successful_crawls"] / stats["total_crawls"]
            stats["average_duration"] = stats["total_duration"] / stats["total_crawls"]
        else:
            stats["success_rate"] = 0.0
            stats["average_duration"] = 0.0
        
        return stats
    
    def reset_stats(self):
        """Reset crawling statistics."""
        
        self.crawl_stats = {
            "total_crawls": 0,
            "successful_crawls": 0,
            "failed_crawls": 0,
            "total_pages": 0,
            "total_duration": 0
        }
        
        logger.info("Crawler statistics reset")
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on crawler components."""
        
        health_status = {
            "crawler_manager": "healthy",
            "crawl4ai_available": CRAWL4AI_AVAILABLE,
            "fallback_available": True,
            "stats": self.get_stats(),
            "timestamp": datetime.now().isoformat()
        }
        
        # Test crawler functionality
        try:
            test_urls = ["https://httpbin.org/html"]
            test_results = await self.crawl_urls(
                urls=test_urls,
                max_pages=1,
                delay=0,
                timeout=10
            )
            
            if test_results and test_results[0].get("success"):
                health_status["test_crawl"] = "passed"
            else:
                health_status["test_crawl"] = "failed"
                health_status["crawler_manager"] = "degraded"
                
        except Exception as e:
            health_status["test_crawl"] = f"error: {str(e)}"
            health_status["crawler_manager"] = "unhealthy"
        
        return health_status
