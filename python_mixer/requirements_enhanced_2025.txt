# Enhanced Cosmic AI Mixer 2025 - Requirements
# Super Advanced Python Mixer with Tavily Integration & Modern AI Frameworks

# Core Dependencies
gradio>=4.0.0
loguru>=0.7.0
pandas>=2.0.0
numpy>=1.24.0
plotly>=5.17.0
aiohttp>=3.9.0
requests>=2.31.0

# AI Framework Dependencies
# CrewAI - Multi-agent AI framework
crewai>=0.28.0
crewai-tools>=0.4.0

# PydanticAI - Structured AI framework
pydantic>=2.5.0
pydantic-ai>=0.0.7

# LangChain - Comprehensive AI framework
langchain>=0.1.0
langchain-community>=0.0.20
langchain-core>=0.1.0
langchain-openai>=0.0.5

# Transcription and Audio Processing
librosa>=0.10.0
soundfile>=0.12.0
pydub>=0.25.0
speech-recognition>=3.10.0

# Document Processing
pypdf2>=3.0.0
python-docx>=0.8.11
openpyxl>=3.1.0
python-magic>=0.4.27

# Email Processing
imaplib2>=3.6
email-validator>=2.1.0
beautifulsoup4>=4.12.0

# Database and Storage
redis>=5.0.0
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
alembic>=1.13.0
pymongo>=4.6.0
motor>=3.3.0
minio>=7.2.0

# API and Web
fastapi>=0.104.0
uvicorn>=0.24.0
httpx>=0.25.0
websockets>=12.0

# Data Science and ML
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0
jupyter>=1.0.0

# Async and Performance
asyncio-mqtt>=0.16.0
aiofiles>=23.2.0
asyncpg>=0.29.0

# Utilities
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.7.0
typer>=0.9.0
pyyaml>=6.0.1
toml>=0.10.2

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.9.0
flake8>=6.1.0
mypy>=1.6.0

# AI Model Dependencies (Required for unified system)
openai>=1.3.0
anthropic>=0.7.0
google-generativeai>=0.3.0
transformers>=4.35.0
torch>=2.1.0
sentence-transformers>=2.2.0

# NVIDIA NeMo for STT (if using local installation)
# nemo-toolkit[asr]>=1.20.0
# torchaudio>=2.1.0

# HVAC and IoT Specific
# paho-mqtt>=1.6.0
# modbus-tk>=1.1.3
# pyserial>=3.5

# Monitoring and Observability
prometheus-client>=0.19.0
psutil>=5.9.0

# Security
cryptography>=41.0.0
bcrypt>=4.1.0
passlib>=1.7.4

# Image Processing (for multi-modal AI)
pillow>=10.1.0
opencv-python>=4.8.0

# Time Series and Analytics
arrow>=1.3.0
pendulum>=2.1.2

# Configuration Management
hydra-core>=1.3.0
omegaconf>=2.3.0

# Caching and Performance
diskcache>=5.6.0
joblib>=1.3.0

# Networking and Communication
paramiko>=3.3.0
fabric>=3.2.0

# File Processing
watchdog>=3.0.0
pathlib2>=2.3.7

# Data Validation
cerberus>=1.3.4
marshmallow>=3.20.0

# Workflow and Task Management
celery>=5.3.0
kombu>=5.3.0

# API Documentation
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0

# Environment and Deployment
docker>=6.1.0
kubernetes>=28.1.0

# Additional Utilities
tqdm>=4.66.0
colorama>=0.4.6
tabulate>=0.9.0
