#!/usr/bin/env python3
"""
🚀 UNIFIED COSMIC SYSTEM LAUNCHER 🚀
===================================

Ultimate launcher for the complete unified cosmic system with Docker orchestration,
health monitoring, and intelligent service management.

Features:
- 🐳 Docker Compose orchestration
- 🏥 Health monitoring and service checks
- 🔧 Automatic dependency resolution
- 📊 Real-time system status
- 🌟 Cosmic interface integration
- ⚡ NVIDIA GPU detection and setup
"""

import os
import sys
import subprocess
import time
import asyncio
import aiohttp
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import signal
from loguru import logger

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


class UnifiedCosmicSystemLauncher:
    """🚀 Ultimate system launcher for unified cosmic interface."""
    
    def __init__(self):
        self.docker_compose_file = current_dir / "docker-compose.unified.yml"
        self.services = {
            "unified-python-mixer": {"port": 7860, "health_endpoint": "/health"},
            "nemo-stt-polish": {"port": 8765, "health_endpoint": "/health"},
            "postgres-unified": {"port": 5432, "health_endpoint": None},
            "mongodb-unified": {"port": 27017, "health_endpoint": None},
            "redis-unified": {"port": 6379, "health_endpoint": None},
            "whisper-stt": {"port": 9000, "health_endpoint": "/health"},
            "monitoring": {"port": 3000, "health_endpoint": None},
            "nginx-proxy": {"port": 80, "health_endpoint": None}
        }
        
        self.required_env_vars = [
            "TAVILY_API_KEY",
            "OPENAI_API_KEY", 
            "ANTHROPIC_API_KEY"
        ]
        
        self.system_status = {
            "docker_available": False,
            "nvidia_available": False,
            "services_running": {},
            "env_configured": False,
            "startup_time": None
        }
    
    def check_system_requirements(self) -> bool:
        """Check all system requirements."""
        logger.info("🔍 Checking system requirements...")
        
        # Check Docker
        self.system_status["docker_available"] = self._check_docker()
        
        # Check NVIDIA Docker runtime
        self.system_status["nvidia_available"] = self._check_nvidia_docker()
        
        # Check environment variables
        self.system_status["env_configured"] = self._check_environment()
        
        # Check disk space
        disk_ok = self._check_disk_space()
        
        # Check network ports
        ports_ok = self._check_network_ports()
        
        all_requirements_met = (
            self.system_status["docker_available"] and
            disk_ok and
            ports_ok
        )
        
        if all_requirements_met:
            logger.success("✅ All system requirements met!")
        else:
            logger.error("❌ Some system requirements not met")
        
        return all_requirements_met
    
    def _check_docker(self) -> bool:
        """Check if Docker is available."""
        try:
            result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                logger.success(f"✅ Docker: {result.stdout.strip()}")
                
                # Check Docker Compose
                result = subprocess.run(
                    ["docker-compose", "--version"], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
                if result.returncode == 0:
                    logger.success(f"✅ Docker Compose: {result.stdout.strip()}")
                    return True
                else:
                    logger.error("❌ Docker Compose not available")
                    return False
            else:
                logger.error("❌ Docker not available")
                return False
        except Exception as e:
            logger.error(f"❌ Docker check failed: {e}")
            return False
    
    def _check_nvidia_docker(self) -> bool:
        """Check if NVIDIA Docker runtime is available."""
        try:
            result = subprocess.run(
                ["docker", "run", "--rm", "--gpus", "all", "nvidia/cuda:11.8-base-ubuntu20.04", "nvidia-smi"], 
                capture_output=True, 
                text=True, 
                timeout=30
            )
            if result.returncode == 0:
                logger.success("✅ NVIDIA Docker runtime available")
                return True
            else:
                logger.warning("⚠️ NVIDIA Docker runtime not available - STT will use CPU fallback")
                return False
        except Exception as e:
            logger.warning(f"⚠️ NVIDIA Docker check failed: {e}")
            return False
    
    def _check_environment(self) -> bool:
        """Check environment variables."""
        missing_vars = []
        
        for var in self.required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.warning(f"⚠️ Missing environment variables: {', '.join(missing_vars)}")
            logger.info("💡 Create .env file with required API keys")
            return False
        else:
            logger.success("✅ Environment variables configured")
            return True
    
    def _check_disk_space(self) -> bool:
        """Check available disk space."""
        try:
            import shutil
            total, used, free = shutil.disk_usage(current_dir)
            free_gb = free // (1024**3)
            
            if free_gb >= 10:  # Require at least 10GB free
                logger.success(f"✅ Disk space: {free_gb}GB available")
                return True
            else:
                logger.error(f"❌ Insufficient disk space: {free_gb}GB (minimum 10GB required)")
                return False
        except Exception as e:
            logger.warning(f"⚠️ Disk space check failed: {e}")
            return True  # Don't block on this
    
    def _check_network_ports(self) -> bool:
        """Check if required network ports are available."""
        import socket
        
        required_ports = [7860, 8765, 5432, 27017, 6379, 9000, 3000, 80]
        busy_ports = []
        
        for port in required_ports:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:  # Port is busy
                busy_ports.append(port)
        
        if busy_ports:
            logger.warning(f"⚠️ Ports already in use: {busy_ports}")
            logger.info("💡 Stop conflicting services or change port configuration")
            return False
        else:
            logger.success("✅ All required ports available")
            return True
    
    def setup_environment(self):
        """Setup environment and configuration files."""
        logger.info("🔧 Setting up environment...")
        
        # Create necessary directories
        directories = [
            "data", "logs", "config", "audio_temp", "audio_processed",
            "documents", "exports", "nemo_models", "nemo_cache", 
            "whisper_models", "sql_init", "mongo_init", "nginx", "monitoring"
        ]
        
        for directory in directories:
            dir_path = current_dir / directory
            dir_path.mkdir(exist_ok=True)
            logger.success(f"✅ Created directory: {directory}")
        
        # Create .env file if it doesn't exist
        env_file = current_dir / ".env"
        if not env_file.exists():
            env_content = """# Unified Cosmic System Environment Configuration

# API Keys (required for full functionality)
TAVILY_API_KEY=your_tavily_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# System Configuration
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
POSTGRES_DB=hvac_db
POSTGRES_USER=hvac_user
POSTGRES_PASSWORD=hvac_password

MONGO_INITDB_ROOT_USERNAME=hvac_user
MONGO_INITDB_ROOT_PASSWORD=hvac_password
MONGO_INITDB_DATABASE=hvac_db

# Service URLs
NEMO_SERVICE_URL=http://nemo-stt-polish:8765
REDIS_URL=redis://redis-unified:6379
POSTGRES_URL=**********************************************************/hvac_db
MONGODB_URL=***************************************************************

# Gradio Configuration
GRADIO_SERVER_NAME=0.0.0.0
GRADIO_SERVER_PORT=7860
"""
            
            with open(env_file, 'w') as f:
                f.write(env_content)
            
            logger.success("✅ Created .env file template")
            logger.info("📝 Please edit .env file with your API keys")
        
        # Create basic nginx configuration
        nginx_conf = current_dir / "nginx" / "nginx.conf"
        if not nginx_conf.exists():
            nginx_content = """events {
    worker_connections 1024;
}

http {
    upstream python_mixer {
        server unified-python-mixer:7860;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            proxy_pass http://python_mixer;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}"""
            
            with open(nginx_conf, 'w') as f:
                f.write(nginx_content)
            
            logger.success("✅ Created nginx configuration")
    
    def start_services(self) -> bool:
        """Start all services using Docker Compose."""
        logger.info("🚀 Starting unified cosmic system services...")
        
        try:
            # Pull latest images
            logger.info("📥 Pulling Docker images...")
            subprocess.run([
                "docker-compose", "-f", str(self.docker_compose_file), "pull"
            ], check=True)
            
            # Start services
            logger.info("🐳 Starting Docker services...")
            subprocess.run([
                "docker-compose", "-f", str(self.docker_compose_file), "up", "-d"
            ], check=True)
            
            self.system_status["startup_time"] = datetime.now()
            logger.success("✅ Services started successfully!")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to start services: {e}")
            return False
    
    async def wait_for_services(self) -> bool:
        """Wait for all services to be healthy."""
        logger.info("⏳ Waiting for services to be ready...")
        
        max_wait_time = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            all_healthy = True
            
            for service_name, config in self.services.items():
                if config["health_endpoint"]:
                    healthy = await self._check_service_health(
                        service_name, 
                        config["port"], 
                        config["health_endpoint"]
                    )
                    self.system_status["services_running"][service_name] = healthy
                    
                    if not healthy:
                        all_healthy = False
                else:
                    # For services without health endpoints, check if port is open
                    healthy = await self._check_port_open("localhost", config["port"])
                    self.system_status["services_running"][service_name] = healthy
                    
                    if not healthy:
                        all_healthy = False
            
            if all_healthy:
                logger.success("✅ All services are healthy!")
                return True
            
            logger.info("⏳ Waiting for services to be ready...")
            await asyncio.sleep(10)
        
        logger.error("❌ Timeout waiting for services to be ready")
        return False
    
    async def _check_service_health(self, service_name: str, port: int, endpoint: str) -> bool:
        """Check if a service is healthy."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"http://localhost:{port}{endpoint}", 
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        logger.success(f"✅ {service_name} is healthy")
                        return True
                    else:
                        logger.warning(f"⚠️ {service_name} returned status {response.status}")
                        return False
        except Exception as e:
            logger.warning(f"⚠️ {service_name} health check failed: {e}")
            return False
    
    async def _check_port_open(self, host: str, port: int) -> bool:
        """Check if a port is open."""
        try:
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port), 
                timeout=5
            )
            writer.close()
            await writer.wait_closed()
            return True
        except Exception:
            return False
    
    def display_system_status(self):
        """Display comprehensive system status."""
        logger.info("📊 System Status Summary:")
        logger.info("=" * 60)
        
        # System requirements
        logger.info("🔧 System Requirements:")
        logger.info(f"  Docker: {'✅' if self.system_status['docker_available'] else '❌'}")
        logger.info(f"  NVIDIA: {'✅' if self.system_status['nvidia_available'] else '⚠️'}")
        logger.info(f"  Environment: {'✅' if self.system_status['env_configured'] else '⚠️'}")
        
        # Services status
        logger.info("\n🐳 Services Status:")
        for service_name, status in self.system_status["services_running"].items():
            logger.info(f"  {service_name}: {'✅' if status else '❌'}")
        
        # Access URLs
        logger.info("\n🌐 Access URLs:")
        logger.info("  🌟 Unified Cosmic Interface: http://localhost:7860")
        logger.info("  🎤 NeMo STT Service: http://localhost:8765")
        logger.info("  📊 Monitoring Dashboard: http://localhost:3000")
        logger.info("  🌐 Nginx Proxy: http://localhost:80")
        
        # Startup time
        if self.system_status["startup_time"]:
            uptime = datetime.now() - self.system_status["startup_time"]
            logger.info(f"\n⏱️ System Uptime: {str(uptime).split('.')[0]}")
        
        logger.info("=" * 60)
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info("🛑 Received shutdown signal, stopping services...")
            self.stop_services()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def stop_services(self):
        """Stop all services."""
        logger.info("🛑 Stopping unified cosmic system services...")
        
        try:
            subprocess.run([
                "docker-compose", "-f", str(self.docker_compose_file), "down"
            ], check=True)
            
            logger.success("✅ Services stopped successfully!")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to stop services: {e}")
    
    async def run(self):
        """🚀 Main launcher method."""
        logger.info("🌟 UNIFIED COSMIC SYSTEM LAUNCHER 2025 🌟")
        logger.info("=" * 60)
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Check system requirements
        if not self.check_system_requirements():
            logger.error("❌ System requirements not met!")
            return False
        
        # Setup environment
        self.setup_environment()
        
        # Start services
        if not self.start_services():
            logger.error("❌ Failed to start services!")
            return False
        
        # Wait for services to be ready
        if not await self.wait_for_services():
            logger.error("❌ Services failed to start properly!")
            return False
        
        # Display system status
        self.display_system_status()
        
        # Keep running
        logger.info("🌟 Unified Cosmic System is running!")
        logger.info("Press Ctrl+C to stop the system")
        
        try:
            while True:
                await asyncio.sleep(60)  # Check every minute
                # Could add periodic health checks here
        except KeyboardInterrupt:
            logger.info("🛑 Shutdown requested by user")
        finally:
            self.stop_services()
        
        return True


def main():
    """Main entry point."""
    launcher = UnifiedCosmicSystemLauncher()
    asyncio.run(launcher.run())


if __name__ == "__main__":
    main()
