"""
🎵 Enhanced Transcription Component - NVIDIA STT Integration
==========================================================

Advanced transcription component with NVIDIA NeMo STT integration,
confidence metrics, HVAC keyword enhancement, and cosmic design.

Features:
- 🎤 NVIDIA NeMo Polish STT integration
- 📊 Real-time confidence metrics
- 🔧 HVAC keyword enhancement
- 🎯 Multi-format audio support (M4A, MP3, WAV)
- 🌟 Cosmic design with animations
"""

import asyncio
import json
import tempfile
import os
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import aiohttp
import gradio as gr
from loguru import logger

from .cosmic_styles import CosmicStyles


class EnhancedTranscriptionProcessor:
    """Enhanced transcription processor with NVIDIA NeMo integration."""
    
    def __init__(self, nemo_service_url: str = "http://localhost:8765"):
        self.nemo_service_url = nemo_service_url
        self.transcription_history = []
        self.hvac_keywords = [
            "klimatyzacja", "klimatyzator", "split", "multi-split", "VRF",
            "serwis", "montaż", "naprawa", "awaria", "konserwacja",
            "LG", "Daikin", "Mitsubishi", "Samsung", "Gree",
            "chłodzenie", "ogrzewanie", "wentylacja", "filtr",
            "pilot", "sterowanie", "temperatura", "wilgotność"
        ]
    
    async def test_nemo_service(self) -> bool:
        """Test if NVIDIA NeMo service is available."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.nemo_service_url}/health", timeout=5) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data.get("status") == "healthy"
            return False
        except Exception as e:
            logger.warning(f"NeMo service test failed: {e}")
            return False
    
    async def transcribe_audio(self, audio_file_path: str, config: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Transcribe audio file using NVIDIA NeMo.
        
        Args:
            audio_file_path: Path to audio file
            config: Optional transcription configuration
            
        Returns:
            Dict containing transcription results and metrics
        """
        if config is None:
            config = {
                "hvac_context": True,
                "keywords": self.hvac_keywords,
                "language": "pl"
            }
        
        try:
            # Check if service is available
            service_available = await self.test_nemo_service()
            
            if not service_available:
                return await self._fallback_transcription(audio_file_path)
            
            # Prepare transcription request
            transcription_data = await self._transcribe_with_nemo(audio_file_path, config)
            
            # Enhance with HVAC context
            enhanced_data = self._enhance_hvac_transcription(transcription_data, config)
            
            # Add to history
            self.transcription_history.append(enhanced_data)
            if len(self.transcription_history) > 100:  # Keep last 100
                self.transcription_history.pop(0)
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "error": str(e),
                "timestamp": datetime.now(),
                "service_used": "error",
                "processing_time": 0.0
            }
    
    async def _transcribe_with_nemo(self, audio_file_path: str, config: Dict) -> Dict[str, Any]:
        """Transcribe using NVIDIA NeMo service."""
        start_time = datetime.now()
        
        try:
            async with aiohttp.ClientSession() as session:
                # Prepare form data
                data = aiohttp.FormData()
                data.add_field('config', json.dumps(config))
                
                # Add audio file
                with open(audio_file_path, 'rb') as f:
                    data.add_field('audio', f, filename=os.path.basename(audio_file_path))
                
                # Make transcription request
                async with session.post(
                    f"{self.nemo_service_url}/transcribe",
                    data=data,
                    timeout=300  # 5 minutes timeout
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        processing_time = (datetime.now() - start_time).total_seconds()
                        
                        return {
                            "text": result.get("text", ""),
                            "confidence": result.get("confidence", 0.85),
                            "language": result.get("language", "pl"),
                            "model": result.get("model", "nemo"),
                            "segments": result.get("segments", []),
                            "service_used": "nvidia_nemo",
                            "processing_time": processing_time,
                            "timestamp": datetime.now(),
                            "audio_duration": self._estimate_audio_duration(audio_file_path)
                        }
                    else:
                        error_text = await response.text()
                        raise Exception(f"NeMo service error: {response.status} - {error_text}")
                        
        except Exception as e:
            logger.error(f"NeMo transcription failed: {e}")
            raise
    
    async def _fallback_transcription(self, audio_file_path: str) -> Dict[str, Any]:
        """Fallback transcription when NeMo is not available."""
        logger.warning("Using fallback transcription (NeMo service unavailable)")
        
        # Simulate transcription for demo purposes
        await asyncio.sleep(2)  # Simulate processing time
        
        return {
            "text": "Przykładowa transkrypcja - serwis NeMo niedostępny. Proszę sprawdzić połączenie z usługą NVIDIA NeMo.",
            "confidence": 0.5,
            "language": "pl",
            "model": "fallback",
            "segments": [{
                "start": 0.0,
                "end": 5.0,
                "text": "Przykładowa transkrypcja - serwis NeMo niedostępny",
                "confidence": 0.5
            }],
            "service_used": "fallback",
            "processing_time": 2.0,
            "timestamp": datetime.now(),
            "audio_duration": 5.0,
            "warning": "NVIDIA NeMo service unavailable - using fallback"
        }
    
    def _enhance_hvac_transcription(self, transcription_data: Dict, config: Dict) -> Dict[str, Any]:
        """Enhance transcription with HVAC-specific processing."""
        if not config.get("hvac_context", False):
            return transcription_data
        
        text = transcription_data.get("text", "")
        
        # Apply HVAC corrections
        corrected_text = self._apply_hvac_corrections(text)
        
        # Extract HVAC entities
        hvac_entities = self._extract_hvac_entities(corrected_text)
        
        # Calculate HVAC relevance score
        hvac_relevance = self._calculate_hvac_relevance(corrected_text)
        
        # Enhance transcription data
        enhanced_data = transcription_data.copy()
        enhanced_data.update({
            "text": corrected_text,
            "original_text": text,
            "hvac_entities": hvac_entities,
            "hvac_relevance": hvac_relevance,
            "hvac_enhanced": True,
            "keywords_found": [kw for kw in self.hvac_keywords if kw.lower() in corrected_text.lower()]
        })
        
        return enhanced_data
    
    def _apply_hvac_corrections(self, text: str) -> str:
        """Apply HVAC-specific corrections to transcribed text."""
        corrections = {
            "klimatyzator": ["klimatyzator", "klimatyzacja"],
            "split": ["split", "splitu", "split system"],
            "serwis": ["serwis", "service"],
            "montaż": ["montaż", "montażu", "instalacja"],
            "naprawa": ["naprawa", "naprawy", "remont"],
            "awaria": ["awaria", "awarii", "usterka"],
            "LG": ["LG", "el dżi", "eldżi", "el-dżi"],
            "Daikin": ["Daikin", "dajkin", "daikin"],
            "Mitsubishi": ["Mitsubishi", "mitsubishi", "micubishi"],
            "VRF": ["VRF", "vrf", "variable refrigerant flow"],
            "multi-split": ["multi-split", "multisplit", "multi split"]
        }
        
        corrected_text = text
        for correct_form, variants in corrections.items():
            for variant in variants:
                if variant != correct_form and variant.lower() in corrected_text.lower():
                    # Case-insensitive replacement
                    import re
                    pattern = re.compile(re.escape(variant), re.IGNORECASE)
                    corrected_text = pattern.sub(correct_form, corrected_text)
        
        return corrected_text
    
    def _extract_hvac_entities(self, text: str) -> Dict[str, List[str]]:
        """Extract HVAC-related entities from text."""
        entities = {
            "equipment": [],
            "brands": [],
            "services": [],
            "problems": []
        }
        
        text_lower = text.lower()
        
        # Equipment types
        equipment_keywords = ["klimatyzator", "split", "multi-split", "vrf", "pompa ciepła"]
        entities["equipment"] = [eq for eq in equipment_keywords if eq in text_lower]
        
        # Brands
        brand_keywords = ["lg", "daikin", "mitsubishi", "samsung", "gree", "fujitsu"]
        entities["brands"] = [brand for brand in brand_keywords if brand in text_lower]
        
        # Services
        service_keywords = ["serwis", "montaż", "instalacja", "konserwacja", "naprawa"]
        entities["services"] = [service for service in service_keywords if service in text_lower]
        
        # Problems
        problem_keywords = ["awaria", "usterka", "nie działa", "nie chłodzi", "nie grzeje"]
        entities["problems"] = [problem for problem in problem_keywords if problem in text_lower]
        
        return entities
    
    def _calculate_hvac_relevance(self, text: str) -> float:
        """Calculate HVAC relevance score for the transcription."""
        if not text:
            return 0.0
        
        text_lower = text.lower()
        keyword_matches = sum(1 for keyword in self.hvac_keywords if keyword.lower() in text_lower)
        
        # Calculate relevance based on keyword density
        words = text_lower.split()
        if not words:
            return 0.0
        
        relevance = min(keyword_matches / len(words) * 10, 1.0)  # Scale to 0-1
        return relevance
    
    def _estimate_audio_duration(self, audio_file_path: str) -> float:
        """Estimate audio duration (simplified implementation)."""
        try:
            # This is a simplified estimation - in production, use librosa or similar
            file_size = os.path.getsize(audio_file_path)
            # Rough estimation: 1MB ≈ 1 minute for typical audio
            estimated_duration = file_size / (1024 * 1024) * 60
            return min(estimated_duration, 300)  # Cap at 5 minutes
        except:
            return 30.0  # Default estimate


class EnhancedTranscriptionComponent:
    """Gradio component for enhanced transcription functionality."""
    
    def __init__(self, nemo_service_url: str = "http://localhost:8765"):
        self.transcription_processor = EnhancedTranscriptionProcessor(nemo_service_url)
        self.cosmic_styles = CosmicStyles()
    
    def create_transcription_interface(self) -> Tuple[gr.Group, List[gr.Component]]:
        """
        Create enhanced transcription interface.
        
        Returns:
            Tuple of (interface_group, output_components)
        """
        with gr.Group(elem_classes=["cosmic-card"]) as transcription_group:
            # Header
            gr.HTML("""
            <div class="processing-card">
                <h3 style="color: #667eea; font-size: 1.5rem; margin-bottom: 16px;">
                    🎤 Enhanced NVIDIA NeMo Transcription
                </h3>
                <p style="opacity: 0.8;">
                    Professional Polish transcription with HVAC keyword enhancement
                </p>
            </div>
            """)
            
            # Transcription inputs
            with gr.Row():
                with gr.Column(scale=2):
                    audio_input = gr.Audio(
                        label="🎵 Upload Audio File",
                        type="filepath",
                        elem_classes=["upload-area"]
                    )
                    
                    with gr.Row():
                        hvac_enhancement = gr.Checkbox(
                            label="🔧 HVAC Keyword Enhancement",
                            value=True,
                            elem_classes=["cosmic-input"]
                        )
                        
                        confidence_threshold = gr.Slider(
                            minimum=0.0,
                            maximum=1.0,
                            value=0.7,
                            step=0.1,
                            label="📊 Confidence Threshold",
                            elem_classes=["cosmic-input"]
                        )
                    
                    transcribe_btn = gr.Button(
                        "🚀 Start Transcription",
                        elem_classes=["cosmic-button-primary"],
                        size="lg"
                    )
                
                with gr.Column(scale=1):
                    # Service status
                    service_status = gr.HTML(
                        value="<div class='status-warning'>🔄 Checking NeMo service...</div>",
                        label="Service Status"
                    )
                    
                    # Quick stats
                    gr.HTML("""
                    <div class="transcription-container">
                        <h4>📊 Transcription Features</h4>
                        <ul style="opacity: 0.8; font-size: 0.9rem;">
                            <li>🎤 NVIDIA NeMo Polish STT</li>
                            <li>🔧 HVAC keyword enhancement</li>
                            <li>📊 Real-time confidence metrics</li>
                            <li>🎯 Multi-format support</li>
                        </ul>
                    </div>
                    """)
            
            # Transcription outputs
            with gr.Tabs():
                with gr.Tab("📝 Transcription Result"):
                    transcription_text = gr.Textbox(
                        label="Transcribed Text",
                        lines=8,
                        elem_classes=["cosmic-input"],
                        placeholder="Transcription will appear here..."
                    )
                    
                    confidence_display = gr.HTML(
                        label="Confidence Metrics",
                        elem_classes=["cosmic-card"]
                    )
                
                with gr.Tab("🔧 HVAC Analysis"):
                    hvac_entities = gr.JSON(
                        label="HVAC Entities Detected",
                        elem_classes=["cosmic-card"]
                    )
                    
                    hvac_insights = gr.Markdown(
                        label="HVAC Insights",
                        elem_classes=["cosmic-card"]
                    )
                
                with gr.Tab("📊 Processing Metrics"):
                    processing_metrics = gr.JSON(
                        label="Processing Metrics",
                        elem_classes=["cosmic-card"]
                    )
                
                with gr.Tab("📋 History"):
                    transcription_history = gr.Dataframe(
                        headers=["Timestamp", "Duration", "Confidence", "HVAC Relevance", "Service"],
                        datatype=["str", "str", "str", "str", "str"],
                        label="Recent Transcriptions",
                        elem_classes=["cosmic-card"]
                    )
        
        # Event handlers
        transcribe_btn.click(
            fn=self._perform_transcription,
            inputs=[audio_input, hvac_enhancement, confidence_threshold],
            outputs=[transcription_text, confidence_display, hvac_entities, hvac_insights, processing_metrics, transcription_history]
        )
        
        # Auto-check service status on load
        transcription_group.load(
            fn=self._check_service_status,
            outputs=[service_status]
        )
        
        output_components = [transcription_text, confidence_display, hvac_entities, hvac_insights, processing_metrics, transcription_history]
        
        return transcription_group, output_components
    
    async def _perform_transcription(self, audio_file, hvac_enhancement: bool, confidence_threshold: float) -> Tuple[str, str, Dict, str, Dict, List]:
        """Perform transcription and return formatted results."""
        if audio_file is None:
            empty_result = "Please upload an audio file."
            return empty_result, "", {}, empty_result, {}, []
        
        try:
            # Configure transcription
            config = {
                "hvac_context": hvac_enhancement,
                "keywords": self.transcription_processor.hvac_keywords if hvac_enhancement else [],
                "language": "pl",
                "confidence_threshold": confidence_threshold
            }
            
            # Perform transcription
            result = await self.transcription_processor.transcribe_audio(audio_file, config)
            
            # Format results
            text = result.get("text", "")
            confidence_html = self._format_confidence_display(result)
            entities = result.get("hvac_entities", {})
            insights = self._format_hvac_insights(result)
            metrics = self._format_processing_metrics(result)
            history = self._format_transcription_history()
            
            return text, confidence_html, entities, insights, metrics, history
            
        except Exception as e:
            error_msg = f"❌ Transcription failed: {str(e)}"
            return error_msg, "", {}, error_msg, {}, []
    
    async def _check_service_status(self) -> str:
        """Check and return service status."""
        try:
            service_available = await self.transcription_processor.test_nemo_service()
            
            if service_available:
                return "<div class='status-success'>✅ NVIDIA NeMo service is running</div>"
            else:
                return "<div class='status-warning'>⚠️ NeMo service unavailable - using fallback</div>"
                
        except Exception as e:
            return f"<div class='status-error'>❌ Service check failed: {str(e)}</div>"
    
    def _format_confidence_display(self, result: Dict[str, Any]) -> str:
        """Format confidence metrics display."""
        confidence = result.get("confidence", 0.0)
        processing_time = result.get("processing_time", 0.0)
        service_used = result.get("service_used", "unknown")
        
        # Confidence color based on score
        if confidence >= 0.8:
            confidence_color = "#34a853"  # Green
        elif confidence >= 0.6:
            confidence_color = "#fbbc05"  # Yellow
        else:
            confidence_color = "#ea4335"  # Red
        
        return f"""
        <div class="transcription-container">
            <h4>📊 Transcription Metrics</h4>
            <div style="margin: 16px 0;">
                <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                    <span>Confidence Score:</span>
                    <span style="color: {confidence_color}; font-weight: bold;">{confidence:.2%}</span>
                </div>
                <div class="confidence-meter" style="width: {confidence*100}%; background: {confidence_color};"></div>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                <span>Processing Time:</span>
                <span>{processing_time:.2f}s</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin: 8px 0;">
                <span>Service Used:</span>
                <span>{service_used.replace('_', ' ').title()}</span>
            </div>
        </div>
        """
    
    def _format_hvac_insights(self, result: Dict[str, Any]) -> str:
        """Format HVAC insights."""
        if not result.get("hvac_enhanced", False):
            return "HVAC enhancement was not enabled for this transcription."
        
        hvac_relevance = result.get("hvac_relevance", 0.0)
        keywords_found = result.get("keywords_found", [])
        entities = result.get("hvac_entities", {})
        
        insights = f"""
# 🔧 HVAC Analysis Results

## 📊 HVAC Relevance Score: {hvac_relevance:.2%}

## 🎯 Keywords Detected
"""
        if keywords_found:
            for keyword in keywords_found:
                insights += f"- **{keyword}**\n"
        else:
            insights += "- No HVAC keywords detected\n"
        
        insights += "\n## 🏷️ Entity Analysis\n"
        for entity_type, items in entities.items():
            if items:
                insights += f"**{entity_type.title()}**: {', '.join(items)}\n"
        
        return insights
    
    def _format_processing_metrics(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Format processing metrics."""
        return {
            "timestamp": result.get("timestamp", datetime.now()).strftime('%Y-%m-%d %H:%M:%S'),
            "processing_time_seconds": result.get("processing_time", 0.0),
            "audio_duration_seconds": result.get("audio_duration", 0.0),
            "confidence_score": result.get("confidence", 0.0),
            "service_used": result.get("service_used", "unknown"),
            "model": result.get("model", "unknown"),
            "language": result.get("language", "pl"),
            "hvac_enhanced": result.get("hvac_enhanced", False),
            "hvac_relevance": result.get("hvac_relevance", 0.0),
            "keywords_found_count": len(result.get("keywords_found", [])),
            "segments_count": len(result.get("segments", []))
        }
    
    def _format_transcription_history(self) -> List[List[str]]:
        """Format transcription history for display."""
        history = []
        for transcription in self.transcription_processor.transcription_history[-10:]:  # Last 10
            history.append([
                transcription.get("timestamp", datetime.now()).strftime('%H:%M:%S'),
                f"{transcription.get('audio_duration', 0):.1f}s",
                f"{transcription.get('confidence', 0):.2%}",
                f"{transcription.get('hvac_relevance', 0):.2%}",
                transcription.get("service_used", "unknown").replace('_', ' ').title()
            ])
        
        return history
