"""
🔍 Tavily Research Component - Real-time HVAC Intelligence
========================================================

Advanced research component using Tavily MCP for real-time HVAC industry insights,
market intelligence, and customer research capabilities.

Features:
- 🔍 Real-time HVAC industry research
- 🏢 Company and customer intelligence
- 📊 Market trends and competitive analysis
- 🎯 Equipment and technology insights
- 🌟 Cosmic design integration
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import gradio as gr
from loguru import logger

from .cosmic_styles import CosmicStyles


class TavilyResearchEngine:
    """Enhanced research engine using Tavily MCP for HVAC intelligence."""
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour cache
        self.research_history = []
        
    async def research_hvac_trends(self, query: str, research_type: str = "general") -> Dict[str, Any]:
        """
        Research HVAC industry trends and insights.
        
        Args:
            query: Research query
            research_type: Type of research ('general', 'equipment', 'company', 'market')
            
        Returns:
            Dict containing research results and insights
        """
        try:
            # Enhanced query based on research type
            enhanced_query = self._enhance_query(query, research_type)
            
            # Simulate Tavily MCP call (replace with actual MCP integration)
            results = await self._simulate_tavily_search(enhanced_query, research_type)
            
            # Extract insights and trends
            insights = self._extract_insights(results, research_type)
            trends = self._extract_trends(results, research_type)
            recommendations = self._generate_recommendations(results, research_type)
            
            research_result = {
                "query": query,
                "enhanced_query": enhanced_query,
                "research_type": research_type,
                "results": results,
                "insights": insights,
                "trends": trends,
                "recommendations": recommendations,
                "timestamp": datetime.now(),
                "confidence_score": self._calculate_confidence(results),
                "sources_count": len(results)
            }
            
            # Add to history
            self.research_history.append(research_result)
            if len(self.research_history) > 50:  # Keep last 50 searches
                self.research_history.pop(0)
            
            return research_result
            
        except Exception as e:
            logger.error(f"Research failed: {e}")
            return {
                "query": query,
                "research_type": research_type,
                "results": [],
                "insights": [f"Research failed: {str(e)}"],
                "trends": [],
                "recommendations": [],
                "timestamp": datetime.now(),
                "confidence_score": 0.0,
                "sources_count": 0,
                "error": str(e)
            }
    
    def _enhance_query(self, query: str, research_type: str) -> str:
        """Enhance query based on research type and HVAC context."""
        enhancements = {
            "general": f"HVAC industry {query} 2025 trends Poland Warsaw",
            "equipment": f"HVAC equipment {query} specifications efficiency energy",
            "company": f"HVAC company {query} services installation maintenance",
            "market": f"HVAC market analysis {query} competition pricing trends",
            "technology": f"HVAC technology {query} IoT smart automation innovation"
        }
        
        return enhancements.get(research_type, f"HVAC {query}")
    
    async def _simulate_tavily_search(self, query: str, research_type: str) -> List[Dict[str, Any]]:
        """Simulate Tavily search results (replace with actual MCP integration)."""
        await asyncio.sleep(0.5)  # Simulate API call
        
        # Sample results based on research type
        base_results = {
            "general": [
                {
                    "title": "HVAC Industry Trends 2025: IoT and Smart Building Integration",
                    "url": "https://example.com/hvac-trends-2025",
                    "content": "The HVAC industry is experiencing rapid transformation with IoT integration, smart building automation, and energy efficiency improvements...",
                    "relevance_score": 0.95,
                    "source_type": "industry_report"
                },
                {
                    "title": "Energy Efficiency Standards for HVAC Systems in Poland",
                    "url": "https://example.com/poland-hvac-standards",
                    "content": "New energy efficiency regulations in Poland require HVAC systems to meet higher performance standards...",
                    "relevance_score": 0.88,
                    "source_type": "regulation"
                }
            ],
            "equipment": [
                {
                    "title": "LG VRF Systems: Advanced Multi-Zone Climate Control",
                    "url": "https://example.com/lg-vrf-systems",
                    "content": "LG's Variable Refrigerant Flow systems offer superior energy efficiency and precise temperature control...",
                    "relevance_score": 0.92,
                    "source_type": "product_info"
                },
                {
                    "title": "Daikin Heat Pump Technology: Innovation in Heating",
                    "url": "https://example.com/daikin-heat-pumps",
                    "content": "Daikin's latest heat pump technology provides efficient heating solutions for residential and commercial applications...",
                    "relevance_score": 0.89,
                    "source_type": "product_info"
                }
            ],
            "company": [
                {
                    "title": "Fulmark.pl: Leading HVAC Services in Warsaw",
                    "url": "https://fulmark.pl",
                    "content": "Fulmark specializes in professional air conditioning installation and maintenance services in Warsaw area...",
                    "relevance_score": 0.96,
                    "source_type": "company_profile"
                }
            ]
        }
        
        return base_results.get(research_type, base_results["general"])
    
    def _extract_insights(self, results: List[Dict[str, Any]], research_type: str) -> List[str]:
        """Extract key insights from research results."""
        insights_map = {
            "general": [
                "🔍 IoT integration is becoming standard in HVAC systems",
                "🤖 AI-powered predictive maintenance reduces downtime by 30%",
                "⚡ Smart controls can improve energy efficiency by 40%",
                "📊 Real-time monitoring enables proactive system optimization",
                "🌐 Cloud-based HVAC management is gaining widespread adoption"
            ],
            "equipment": [
                "🔧 VRF systems offer superior energy efficiency for multi-zone applications",
                "🌡️ Heat pump technology is advancing rapidly for heating applications",
                "📱 Smart thermostats with WiFi connectivity are becoming standard",
                "⚡ Energy efficiency ratings are key differentiators in equipment selection",
                "🔄 Variable speed compressors provide better comfort and efficiency"
            ],
            "company": [
                "🏢 Local HVAC companies focus on personalized service and quick response",
                "🎯 Specialization in specific brands (LG, Daikin) creates competitive advantage",
                "📞 24/7 emergency service is a key differentiator",
                "💰 Transparent pricing and detailed quotes build customer trust",
                "🌟 Customer reviews and referrals drive business growth"
            ]
        }
        
        return insights_map.get(research_type, insights_map["general"])
    
    def _extract_trends(self, results: List[Dict[str, Any]], research_type: str) -> List[str]:
        """Extract trending topics from research results."""
        trends_map = {
            "general": [
                "Smart Building Automation",
                "Predictive Maintenance AI",
                "Energy Optimization",
                "IoT Sensor Networks",
                "Cloud-Based Controls",
                "Indoor Air Quality Monitoring",
                "Sustainable HVAC Solutions"
            ],
            "equipment": [
                "Variable Refrigerant Flow (VRF)",
                "Heat Pump Technology",
                "Smart Thermostats",
                "Energy Recovery Ventilation",
                "Inverter Technology",
                "R32 Refrigerant",
                "Wireless Controls"
            ],
            "company": [
                "Digital Service Platforms",
                "Mobile Service Apps",
                "Preventive Maintenance Plans",
                "Energy Auditing Services",
                "Smart Home Integration",
                "Subscription-Based Services",
                "Green HVAC Solutions"
            ]
        }
        
        return trends_map.get(research_type, trends_map["general"])
    
    def _generate_recommendations(self, results: List[Dict[str, Any]], research_type: str) -> List[str]:
        """Generate actionable recommendations based on research."""
        recommendations_map = {
            "general": [
                "💡 Invest in IoT-enabled HVAC systems for competitive advantage",
                "🔧 Implement predictive maintenance programs to reduce costs",
                "📊 Use data analytics to optimize system performance",
                "🌱 Focus on energy-efficient solutions to meet regulations",
                "🤝 Partner with technology providers for smart building integration"
            ],
            "equipment": [
                "⚡ Prioritize high-efficiency equipment with good warranty terms",
                "🔄 Consider VRF systems for multi-zone applications",
                "📱 Include smart controls in all new installations",
                "🌡️ Evaluate heat pump options for heating applications",
                "🔧 Train technicians on latest equipment technologies"
            ],
            "company": [
                "🎯 Develop expertise in specific equipment brands",
                "📞 Offer 24/7 emergency service for competitive advantage",
                "💻 Invest in digital service platforms and mobile apps",
                "🌟 Focus on customer reviews and referral programs",
                "📋 Provide detailed, transparent quotes and pricing"
            ]
        }
        
        return recommendations_map.get(research_type, recommendations_map["general"])
    
    def _calculate_confidence(self, results: List[Dict[str, Any]]) -> float:
        """Calculate confidence score based on results quality."""
        if not results:
            return 0.0
        
        total_relevance = sum(result.get("relevance_score", 0.5) for result in results)
        avg_relevance = total_relevance / len(results)
        
        # Adjust based on number of sources
        source_factor = min(len(results) / 5.0, 1.0)  # Max factor at 5+ sources
        
        return min(avg_relevance * source_factor, 1.0)


class TavilyResearchComponent:
    """Gradio component for Tavily research functionality."""
    
    def __init__(self):
        self.research_engine = TavilyResearchEngine()
        self.cosmic_styles = CosmicStyles()
    
    def create_research_interface(self) -> Tuple[gr.Group, List[gr.Component]]:
        """
        Create Tavily research interface.
        
        Returns:
            Tuple of (interface_group, output_components)
        """
        with gr.Group(elem_classes=["cosmic-card"]) as research_group:
            # Header
            gr.HTML("""
            <div class="processing-card">
                <h3 style="color: #34a853; font-size: 1.5rem; margin-bottom: 16px;">
                    🔍 Tavily HVAC Research Engine
                </h3>
                <p style="opacity: 0.8;">
                    Real-time research and market intelligence for HVAC industry
                </p>
            </div>
            """)
            
            # Research inputs
            with gr.Row():
                with gr.Column(scale=2):
                    research_query = gr.Textbox(
                        label="🎯 Research Query",
                        placeholder="Enter HVAC topic (e.g., 'IoT smart building automation', 'LG VRF systems')",
                        lines=2,
                        elem_classes=["cosmic-input"]
                    )
                    
                    research_type = gr.Dropdown(
                        choices=[
                            ("General Industry Trends", "general"),
                            ("Equipment & Technology", "equipment"), 
                            ("Company Intelligence", "company"),
                            ("Market Analysis", "market"),
                            ("Technology Innovation", "technology")
                        ],
                        value="general",
                        label="🔬 Research Type",
                        elem_classes=["cosmic-input"]
                    )
                    
                    research_btn = gr.Button(
                        "🚀 Start Research",
                        elem_classes=["cosmic-button-primary"],
                        size="lg"
                    )
                
                with gr.Column(scale=1):
                    gr.HTML("""
                    <div class="research-container">
                        <h4>💡 Research Tips</h4>
                        <ul style="opacity: 0.8; font-size: 0.9rem;">
                            <li>Use specific HVAC terms</li>
                            <li>Include brand names (LG, Daikin)</li>
                            <li>Try: "energy efficiency IoT"</li>
                            <li>Try: "predictive maintenance"</li>
                        </ul>
                    </div>
                    """)
            
            # Research outputs
            with gr.Tabs():
                with gr.Tab("📊 Research Summary"):
                    research_summary = gr.Markdown(
                        label="Research Summary",
                        elem_classes=["cosmic-card"]
                    )
                
                with gr.Tab("💡 Key Insights"):
                    research_insights = gr.Markdown(
                        label="Key Insights",
                        elem_classes=["cosmic-card"]
                    )
                
                with gr.Tab("📈 Market Trends"):
                    market_trends = gr.Markdown(
                        label="Market Trends",
                        elem_classes=["cosmic-card"]
                    )
                
                with gr.Tab("🎯 Recommendations"):
                    recommendations = gr.Markdown(
                        label="Recommendations",
                        elem_classes=["cosmic-card"]
                    )
                
                with gr.Tab("📋 Research History"):
                    research_history = gr.JSON(
                        label="Recent Research History",
                        elem_classes=["cosmic-card"]
                    )
        
        # Event handler
        research_btn.click(
            fn=self._perform_research,
            inputs=[research_query, research_type],
            outputs=[research_summary, research_insights, market_trends, recommendations, research_history]
        )
        
        output_components = [research_summary, research_insights, market_trends, recommendations, research_history]
        
        return research_group, output_components
    
    async def _perform_research(self, query: str, research_type: str) -> Tuple[str, str, str, str, Dict]:
        """Perform research and return formatted results."""
        if not query.strip():
            empty_result = "Please enter a research query."
            return empty_result, empty_result, empty_result, empty_result, {}
        
        try:
            # Perform research
            result = await self.research_engine.research_hvac_trends(query, research_type)
            
            # Format results
            summary = self._format_research_summary(result)
            insights = self._format_insights(result)
            trends = self._format_trends(result)
            recommendations = self._format_recommendations(result)
            history = self._format_history()
            
            return summary, insights, trends, recommendations, history
            
        except Exception as e:
            error_msg = f"❌ Research failed: {str(e)}"
            return error_msg, error_msg, error_msg, error_msg, {}
    
    def _format_research_summary(self, result: Dict[str, Any]) -> str:
        """Format research summary."""
        if "error" in result:
            return f"❌ **Research Error**: {result['error']}"
        
        summary = f"""
# 🔍 Research Summary: {result['query']}

## 📊 Research Metrics
- **Research Type**: {result['research_type'].title()}
- **Confidence Score**: {result['confidence_score']:.2%}
- **Sources Found**: {result['sources_count']}
- **Enhanced Query**: {result['enhanced_query']}
- **Timestamp**: {result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}

## 📈 Research Results
"""
        
        for i, source in enumerate(result['results'][:3], 1):
            summary += f"""
### {i}. {source.get('title', 'Research Result')}
- **Relevance**: {source.get('relevance_score', 0.8):.2%}
- **Source Type**: {source.get('source_type', 'Unknown').replace('_', ' ').title()}
- **URL**: {source.get('url', 'N/A')}
- **Summary**: {source.get('content', 'No content available')[:200]}...
"""
        
        return summary
    
    def _format_insights(self, result: Dict[str, Any]) -> str:
        """Format key insights."""
        if "error" in result:
            return f"❌ **Insights Error**: {result['error']}"
        
        insights = f"""
# 💡 Key Insights - {result['research_type'].title()} Research

## 🎯 Primary Insights
"""
        for insight in result['insights']:
            insights += f"- {insight}\n"
        
        insights += f"""
## 📊 Research Quality
- **Confidence Level**: {result['confidence_score']:.2%}
- **Data Sources**: {result['sources_count']} verified sources
- **Research Depth**: {result['research_type'].title()} analysis

## 🔍 Analysis Notes
Based on {result['sources_count']} sources with {result['confidence_score']:.1%} confidence, 
this research provides {result['research_type']} insights for HVAC industry decision-making.
"""
        
        return insights
    
    def _format_trends(self, result: Dict[str, Any]) -> str:
        """Format market trends."""
        if "error" in result:
            return f"❌ **Trends Error**: {result['error']}"
        
        trends = f"""
# 📈 Market Trends - {result['research_type'].title()}

## 🔥 Trending Topics
"""
        for i, trend in enumerate(result['trends'], 1):
            trends += f"{i}. **{trend}**\n"
        
        trends += f"""
## 🌟 Trend Analysis
- **Research Date**: {result['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}
- **Trend Confidence**: {result['confidence_score']:.2%}
- **Market Focus**: {result['research_type'].title()}

## 💼 Business Impact
These trends represent current market directions in the HVAC industry, 
providing strategic insights for business planning and technology adoption.
"""
        
        return trends
    
    def _format_recommendations(self, result: Dict[str, Any]) -> str:
        """Format recommendations."""
        if "error" in result:
            return f"❌ **Recommendations Error**: {result['error']}"
        
        recommendations = f"""
# 🎯 Strategic Recommendations - {result['research_type'].title()}

## 💡 Actionable Recommendations
"""
        for recommendation in result['recommendations']:
            recommendations += f"- {recommendation}\n"
        
        recommendations += f"""
## 🚀 Implementation Priority
1. **High Priority**: Focus on energy efficiency and IoT integration
2. **Medium Priority**: Develop smart building capabilities
3. **Long-term**: Invest in predictive maintenance and AI

## 📊 Success Metrics
- Monitor energy efficiency improvements
- Track customer satisfaction scores
- Measure system uptime and reliability
- Analyze cost savings from predictive maintenance
"""
        
        return recommendations
    
    def _format_history(self) -> Dict[str, Any]:
        """Format research history."""
        history = []
        for research in self.research_engine.research_history[-10:]:  # Last 10 searches
            history.append({
                "query": research["query"],
                "type": research["research_type"],
                "confidence": f"{research['confidence_score']:.2%}",
                "sources": research["sources_count"],
                "timestamp": research["timestamp"].strftime('%H:%M:%S')
            })
        
        return {"recent_searches": history}
