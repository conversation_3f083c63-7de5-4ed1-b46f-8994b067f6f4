"""
Enhanced HVAC CrewAI Implementation
==================================

Advanced multi-agent system for email and transcription processing.
Specialized teams for collaborative HVAC customer intelligence.
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool


@dataclass
class EmailProcessingContext:
    """Context for email processing workflow"""
    email_source: str  # dolores@ or grzegorz@
    email_content: str
    attachments: List[str]
    timestamp: datetime
    customer_id: Optional[str] = None


@dataclass
class TranscriptionContext:
    """Context for M4A transcription processing"""
    audio_file_path: str
    transcription_text: str
    confidence_score: float
    timestamp: datetime
    customer_id: Optional[str] = None


class EmailParserAgent:
    """Specialized agent for parsing HVAC emails"""
    
    def __init__(self, llm):
        self.agent = Agent(
            role='HVAC Email Intelligence Specialist',
            goal='Extract structured data from HVAC customer emails',
            backstory="""Expert in HVAC industry communications with deep
            understanding of technical terminology, customer issues, and
            service requests. Excels at extracting actionable insights.""",
            verbose=True,
            allow_delegation=False,
            llm=llm
        )
    
    def create_parsing_task(self, context: EmailProcessingContext) -> Task:
        """Create email parsing task"""
        return Task(
            description=f"""Parse HVAC email and extract structured data:
            Email: {context.email_content[:500]}...""",
            agent=self.agent,
            expected_output="Structured JSON with customer and technical data"
        )