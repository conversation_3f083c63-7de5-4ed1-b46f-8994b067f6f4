"""
PydanticAI Implementation for HVAC CRM System
============================================

Advanced customer profile system with data validation and structured outputs.
Provides type-safe AI agents for customer data aggregation and intelligence.
"""

from .customer_profile_models import (
    CustomerProfile,
    EmailRecord,
    TranscriptionRecord,
    SentimentScore,
    ServiceRecord,
    EquipmentRecord,
    FinancialSummary,
    RiskLevel,
    SatisfactionPoint,
    CustomerIntelligence,
    CustomerHealthMetrics
)

from .customer_profile_agent import CustomerProfileAgent
from .email_intelligence_agent import EmailIntelligenceAgent
from .transcription_agent import TranscriptionAgent
from .data_aggregation_agent import DataAggregationAgent

__all__ = [
    'CustomerProfile',
    'EmailRecord', 
    'TranscriptionRecord',
    'SentimentScore',
    'ServiceRecord',
    'EquipmentRecord', 
    'FinancialSummary',
    'RiskLevel',
    'SatisfactionPoint',
    'CustomerIntelligence',
    'CustomerHealthMetrics',
    'CustomerProfileAgent',
    'EmailIntelligenceAgent',
    'TranscriptionAgent',
    'DataAggregationAgent'
]