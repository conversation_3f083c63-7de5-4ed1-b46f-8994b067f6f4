"""
Advanced Customer Profile Data Models for HVAC CRM
=================================================

Comprehensive Pydantic models for customer data validation and intelligence.
Designed for multi-source data aggregation with strict type safety.
"""

from datetime import datetime, date
from decimal import Decimal
from enum import Enum
from typing import List, Optional, Dict, Any, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, EmailStr, validator, root_validator
from pydantic.types import PositiveFloat, PositiveInt


class RiskLevel(str, Enum):
    """Customer risk assessment levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SentimentType(str, Enum):
    """Sentiment analysis types"""
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    MIXED = "mixed"


class CommunicationType(str, Enum):
    """Types of customer communications"""
    EMAIL = "email"
    PHONE = "phone"
    SMS = "sms"
    CHAT = "chat"
    VISIT = "visit"


class ServiceStatus(str, Enum):
    """Service order status"""
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    PENDING_PARTS = "pending_parts"

class EquipmentStatus(str, Enum):
    """HVAC equipment status"""
    ACTIVE = "active"
    MAINTENANCE = "maintenance"
    REPAIR = "repair"
    REPLACED = "replaced"
    DECOMMISSIONED = "decommissioned"


class EmailRecord(BaseModel):
    """Individual email communication record"""
    email_id: UUID = Field(default_factory=uuid4)
    sender: EmailStr
    recipient: EmailStr
    subject: str
    content: str
    timestamp: datetime
    thread_id: Optional[str] = None
    attachments: List[str] = Field(default_factory=list)
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    sentiment_type: Optional[SentimentType] = None
    extracted_entities: Dict[str, Any] = Field(default_factory=dict)
    priority: int = Field(1, ge=1, le=5)
    is_processed: bool = False
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class TranscriptionRecord(BaseModel):
    """M4A transcription record with analysis"""
    transcription_id: UUID = Field(default_factory=uuid4)
    audio_file_path: str
    transcription_text: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    language: str = "pl"
    duration_seconds: PositiveFloat
    timestamp: datetime
    speaker_count: Optional[int] = None
    technical_keywords: List[str] = Field(default_factory=list)
    customer_issues: List[str] = Field(default_factory=list)
    equipment_mentioned: List[str] = Field(default_factory=list)
    sentiment_score: Optional[float] = Field(None, ge=-1.0, le=1.0)
    urgency_level: int = Field(1, ge=1, le=5)
    follow_up_required: bool = False
    
    @validator('confidence_score')
    def validate_confidence(cls, v):
        if v < 0.7:
            raise ValueError('Confidence score too low for reliable transcription')
        return v


class SentimentScore(BaseModel):
    """Sentiment analysis result"""
    score: float = Field(ge=-1.0, le=1.0)
    sentiment_type: SentimentType
    confidence: float = Field(ge=0.0, le=1.0)
    timestamp: datetime
    source_type: CommunicationType
    source_id: UUID
    keywords: List[str] = Field(default_factory=list)
    context: Optional[str] = None
class ServiceRecord(BaseModel):
    """HVAC service record"""
    service_id: UUID = Field(default_factory=uuid4)
    service_date: date
    service_type: str
    technician_id: Optional[UUID] = None
    technician_name: Optional[str] = None
    equipment_serviced: List[str] = Field(default_factory=list)
    description: str
    status: ServiceStatus
    cost: Decimal = Field(ge=0)
    duration_hours: PositiveFloat
    customer_satisfaction: Optional[int] = Field(None, ge=1, le=5)
    parts_used: List[Dict[str, Any]] = Field(default_factory=list)
    warranty_period_days: Optional[int] = None
    follow_up_date: Optional[date] = None
    notes: Optional[str] = None


class EquipmentRecord(BaseModel):
    """HVAC equipment tracking"""
    equipment_id: UUID = Field(default_factory=uuid4)
    equipment_type: str  # AC, Heat Pump, Furnace, etc.
    brand: str
    model: str
    serial_number: Optional[str] = None
    installation_date: date
    warranty_expiry: Optional[date] = None
    status: EquipmentStatus
    location: str  # Room/area in building
    capacity: Optional[str] = None  # BTU, kW, etc.
    efficiency_rating: Optional[str] = None
    last_service_date: Optional[date] = None
    next_service_due: Optional[date] = None
    service_history: List[UUID] = Field(default_factory=list)  # Service record IDs
    maintenance_alerts: List[str] = Field(default_factory=list)
    estimated_lifespan_years: Optional[int] = None
    replacement_cost_estimate: Optional[Decimal] = None


class FinancialSummary(BaseModel):
    """Customer financial overview"""
    total_spent: Decimal = Field(ge=0)
    average_order_value: Decimal = Field(ge=0)
    payment_history_score: int = Field(ge=1, le=10)
    outstanding_balance: Decimal = Field(ge=0)
    credit_limit: Optional[Decimal] = None
    last_payment_date: Optional[date] = None
    preferred_payment_method: Optional[str] = None
    invoice_count: PositiveInt
    overdue_invoices: int = Field(ge=0)
    lifetime_value: Decimal = Field(ge=0)
    profitability_score: float = Field(ge=0.0, le=10.0)class SatisfactionPoint(BaseModel):
    """Customer satisfaction tracking point"""
    timestamp: datetime
    score: int = Field(ge=1, le=5)
    source: str  # survey, call, email, etc.
    comments: Optional[str] = None
    service_id: Optional[UUID] = None


class CustomerHealthMetrics(BaseModel):
    """Advanced customer health analytics"""
    health_score: float = Field(ge=0.0, le=100.0)
    churn_probability: float = Field(ge=0.0, le=1.0)
    engagement_score: float = Field(ge=0.0, le=10.0)
    satisfaction_trend: float = Field(ge=-1.0, le=1.0)  # -1 declining, +1 improving
    communication_frequency: float = Field(ge=0.0)  # contacts per month
    service_frequency: float = Field(ge=0.0)  # services per year
    complaint_ratio: float = Field(ge=0.0, le=1.0)
    referral_count: int = Field(ge=0)
    loyalty_score: float = Field(ge=0.0, le=10.0)
    last_updated: datetime


class CustomerIntelligence(BaseModel):
    """AI-powered customer insights"""
    predicted_next_service: Optional[date] = None
    recommended_services: List[str] = Field(default_factory=list)
    upsell_opportunities: List[str] = Field(default_factory=list)
    risk_factors: List[str] = Field(default_factory=list)
    behavioral_patterns: Dict[str, Any] = Field(default_factory=dict)
    seasonal_preferences: Dict[str, Any] = Field(default_factory=dict)
    communication_preferences: Dict[str, Any] = Field(default_factory=dict)
    equipment_upgrade_recommendations: List[Dict[str, Any]] = Field(default_factory=list)
    maintenance_optimization: Dict[str, Any] = Field(default_factory=dict)
    cost_optimization_suggestions: List[str] = Field(default_factory=list)class CustomerProfile(BaseModel):
    """
    Comprehensive Customer Profile - The Ultimate HVAC Customer Intelligence
    
    Aggregates all customer data sources into a unified, validated profile
    with AI-powered insights and predictive analytics.
    """
    # Core Identity
    customer_id: UUID = Field(default_factory=uuid4)
    name: str = Field(min_length=1, max_length=200)
    email: EmailStr
    phone: Optional[str] = Field(None, regex=r'^\+?[\d\s\-\(\)]+$')
    company_name: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    postal_code: Optional[str] = None
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_contact: Optional[datetime] = None
    last_service: Optional[datetime] = None
    
    # Communication History
    emails: List[EmailRecord] = Field(default_factory=list)
    transcriptions: List[TranscriptionRecord] = Field(default_factory=list)
    sentiment_history: List[SentimentScore] = Field(default_factory=list)
    communication_summary: Dict[str, Any] = Field(default_factory=dict)
    
    # Business Data
    service_history: List[ServiceRecord] = Field(default_factory=list)
    equipment: List[EquipmentRecord] = Field(default_factory=list)
    financial_data: FinancialSummary
    satisfaction_history: List[SatisfactionPoint] = Field(default_factory=list)
    
    # Intelligence & Analytics
    health_metrics: CustomerHealthMetrics
    ai_insights: CustomerIntelligence
    risk_assessment: RiskLevel = RiskLevel.LOW
    customer_tier: str = "standard"  # standard, premium, vip
    
    # Metadata
    data_sources: List[str] = Field(default_factory=list)
    data_quality_score: float = Field(ge=0.0, le=100.0, default=0.0)
    last_ai_analysis: Optional[datetime] = None
    tags: List[str] = Field(default_factory=list)
    notes: Optional[str] = None
    
    @root_validator
    def validate_profile_completeness(cls, values):
        """Ensure profile has minimum required data"""
        required_fields = ['name', 'email', 'financial_data', 'health_metrics']
        for field in required_fields:
            if not values.get(field):
                raise ValueError(f'Required field {field} is missing')
        return values    
    @validator('updated_at', always=True)
    def set_updated_at(cls, v):
        return datetime.utcnow()
    
    def calculate_completeness_score(self) -> float:
        """Calculate profile data completeness percentage"""
        total_fields = 0
        filled_fields = 0
        
        # Core fields (weight: 2)
        core_fields = ['name', 'email', 'phone', 'address']
        for field in core_fields:
            total_fields += 2
            if getattr(self, field):
                filled_fields += 2
        
        # Communication data (weight: 3)
        if self.emails:
            filled_fields += 3
        if self.transcriptions:
            filled_fields += 3
        total_fields += 6
        
        # Business data (weight: 4)
        if self.service_history:
            filled_fields += 4
        if self.equipment:
            filled_fields += 4
        total_fields += 8
        
        return (filled_fields / total_fields) * 100 if total_fields > 0 else 0
    
    def get_latest_sentiment(self) -> Optional[SentimentScore]:
        """Get most recent sentiment analysis"""
        if not self.sentiment_history:
            return None
        return max(self.sentiment_history, key=lambda x: x.timestamp)
    
    def get_equipment_due_for_service(self) -> List[EquipmentRecord]:
        """Get equipment that needs service"""
        today = date.today()
        return [
            eq for eq in self.equipment 
            if eq.next_service_due and eq.next_service_due <= today
        ]
    
    def calculate_lifetime_value(self) -> Decimal:
        """Calculate customer lifetime value"""
        return self.financial_data.lifetime_value
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat(),
            UUID: lambda v: str(v),
            Decimal: lambda v: float(v)
        }