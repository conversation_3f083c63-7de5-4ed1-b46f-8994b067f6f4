#!/usr/bin/env python3
"""
🗄️ Enhanced Database Configuration & Integration
===============================================

Leverages existing database configurations from the project.
Optimized for CrewAI + Gemma Vision + PydanticAI integration.
"""

import os
import asyncio
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from contextlib import asynccontextmanager

import psycopg2
from psycopg2.pool import ThreadedConnectionPool
from pymongo import MongoClient
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
import yaml

# Load existing configuration
CONFIG_PATH = "/home/<USER>/HVAC/unifikacja/python_mixer/config/enhanced_config.yaml"

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """Unified database configuration"""
    # PostgreSQL (from existing config)
    postgres_host: str = "**************"
    postgres_port: int = 5432
    postgres_db: str = "hvacdb"
    postgres_user: str = "hvacdb"
    postgres_password: str = "blaeritipol"
    
    # MongoDB (from existing config)
    mongo_host: str = "**************"
    mongo_port: int = 27017
    mongo_user: str = "Koldbringer"
    mongo_password: str = "blaeiritpol"
    mongo_db: str = "hvac_enhanced_crm"
    
    # Redis (from existing config)
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    
    # MinIO (from existing config)
    minio_host: str = "**************"
    minio_port: int = 9000
    minio_access_key: str = "koldbringer"
    minio_secret_key: str = "Blaeritipol1"
    minio_bucket: str = "hvac-documents"


class EnhancedDatabaseManager:
    """
    Enhanced database manager with async support
    Integrates all existing database configurations
    """
    
    def __init__(self, config: DatabaseConfig = None):
        self.config = config or DatabaseConfig()
        self.postgres_pool = None
        self.mongo_client = None
        self.mongo_async_client = None
        self.redis_client = None
        
    async def initialize_all(self):
        """Initialize all database connections"""
        logger.info("🚀 Initializing enhanced database connections...")
        
        await self._init_postgres()
        await self._init_mongodb()
        await self._init_redis()
        
        logger.info("✅ All database connections initialized")
    
    async def _init_postgres(self):
        """Initialize PostgreSQL connection pool"""
        try:
            # Create connection pool for better performance
            self.postgres_pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=20,
                host=self.config.postgres_host,
                port=self.config.postgres_port,
                database=self.config.postgres_db,
                user=self.config.postgres_user,
                password=self.config.postgres_password
            )
            
            # Test connection
            conn = self.postgres_pool.getconn()
            with conn.cursor() as cur:
                cur.execute("SELECT version();")
                version = cur.fetchone()
                logger.info(f"✅ PostgreSQL connected: {version[0][:50]}...")
            self.postgres_pool.putconn(conn)
            
            # Create enhanced tables if they don't exist
            await self._create_enhanced_tables()
            
        except Exception as e:
            logger.error(f"❌ PostgreSQL connection failed: {e}")
            raise
    
    async def _init_mongodb(self):
        """Initialize MongoDB connections (sync and async)"""
        try:
            # Sync client for compatibility
            mongo_url = f"mongodb://{self.config.mongo_user}:{self.config.mongo_password}@{self.config.mongo_host}:{self.config.mongo_port}"
            self.mongo_client = MongoClient(mongo_url)
            
            # Async client for better performance
            self.mongo_async_client = AsyncIOMotorClient(mongo_url)
            
            # Test connection
            server_info = self.mongo_client.server_info()
            logger.info(f"✅ MongoDB connected: version {server_info['version']}")
            
            # Create indexes for better performance
            await self._create_mongodb_indexes()
            
        except Exception as e:
            logger.error(f"❌ MongoDB connection failed: {e}")
            raise
    
    async def _init_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                password=self.config.redis_password,
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("✅ Redis connected")
            
        except Exception as e:
            logger.error(f"❌ Redis connection failed: {e}")
            raise
    
    async def _create_enhanced_tables(self):
        """Create enhanced tables for CrewAI + Gemma Vision integration"""
        tables_sql = """
        -- Enhanced customer profiles with visual data
        CREATE TABLE IF NOT EXISTS enhanced_customer_profiles (
            id SERIAL PRIMARY KEY,
            customer_id VARCHAR(255) UNIQUE NOT NULL,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            phone VARCHAR(50),
            company VARCHAR(255),
            address TEXT,
            city VARCHAR(100),
            postal_code VARCHAR(20),
            profile_completeness FLOAT DEFAULT 0.0,
            health_score FLOAT DEFAULT 0.0,
            churn_probability FLOAT DEFAULT 0.0,
            lifetime_value DECIMAL(10,2) DEFAULT 0.0,
            risk_level VARCHAR(20) DEFAULT 'low',
            customer_tier VARCHAR(20) DEFAULT 'standard',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Visual equipment analysis results
        CREATE TABLE IF NOT EXISTS equipment_visual_analyses (
            id SERIAL PRIMARY KEY,
            customer_id VARCHAR(255) NOT NULL,
            image_path VARCHAR(500) NOT NULL,
            equipment_type VARCHAR(100),
            brand VARCHAR(100),
            model VARCHAR(100),
            condition_assessment VARCHAR(50),
            issues_detected JSONB,
            maintenance_recommendations JSONB,
            confidence_score FLOAT,
            processed_tokens INTEGER,
            analysis_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES enhanced_customer_profiles(customer_id)
        );
        
        -- CrewAI task execution logs
        CREATE TABLE IF NOT EXISTS crewai_task_logs (
            id SERIAL PRIMARY KEY,
            customer_id VARCHAR(255),
            crew_type VARCHAR(100),
            task_name VARCHAR(255),
            agent_name VARCHAR(255),
            execution_start TIMESTAMP,
            execution_end TIMESTAMP,
            status VARCHAR(50),
            results JSONB,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Enhanced email analysis with visual context
        CREATE TABLE IF NOT EXISTS enhanced_email_analysis_v2 (
            id SERIAL PRIMARY KEY,
            email_id VARCHAR(255) UNIQUE NOT NULL,
            customer_id VARCHAR(255),
            sender VARCHAR(255),
            subject TEXT,
            content TEXT,
            timestamp TIMESTAMP,
            analysis_type VARCHAR(50),
            intent VARCHAR(100),
            sentiment VARCHAR(50),
            priority VARCHAR(50),
            customer_info JSONB,
            equipment_info JSONB,
            action_items JSONB,
            service_type VARCHAR(100),
            urgency_level INTEGER,
            estimated_cost DECIMAL(10,2),
            location VARCHAR(255),
            processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            model_used VARCHAR(100),
            confidence_score FLOAT,
            visual_context_available BOOLEAN DEFAULT FALSE,
            related_images JSONB
        );
        
        -- Customer communication timeline
        CREATE TABLE IF NOT EXISTS customer_communication_timeline (
            id SERIAL PRIMARY KEY,
            customer_id VARCHAR(255) NOT NULL,
            communication_type VARCHAR(50), -- email, phone, visit, image
            content TEXT,
            sentiment_score FLOAT,
            timestamp TIMESTAMP,
            metadata JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Equipment maintenance predictions
        CREATE TABLE IF NOT EXISTS equipment_maintenance_predictions (
            id SERIAL PRIMARY KEY,
            customer_id VARCHAR(255) NOT NULL,
            equipment_id VARCHAR(255),
            equipment_type VARCHAR(100),
            predicted_maintenance_date DATE,
            maintenance_type VARCHAR(100),
            confidence_score FLOAT,
            cost_estimate DECIMAL(10,2),
            priority_level INTEGER,
            prediction_model VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- Create indexes for better performance
        CREATE INDEX IF NOT EXISTS idx_customer_profiles_email ON enhanced_customer_profiles(email);
        CREATE INDEX IF NOT EXISTS idx_equipment_analyses_customer ON equipment_visual_analyses(customer_id);
        CREATE INDEX IF NOT EXISTS idx_email_analysis_customer ON enhanced_email_analysis_v2(customer_id);
        CREATE INDEX IF NOT EXISTS idx_communication_timeline_customer ON customer_communication_timeline(customer_id);
        CREATE INDEX IF NOT EXISTS idx_maintenance_predictions_customer ON equipment_maintenance_predictions(customer_id);
        """
        
        try:
            conn = self.postgres_pool.getconn()
            with conn.cursor() as cur:
                cur.execute(tables_sql)
            conn.commit()
            self.postgres_pool.putconn(conn)
            logger.info("✅ Enhanced PostgreSQL tables created/verified")
        except Exception as e:
            logger.error(f"❌ Error creating PostgreSQL tables: {e}")
            raise
    
    async def _create_mongodb_indexes(self):
        """Create MongoDB indexes for better performance"""
        try:
            db = self.mongo_async_client[self.config.mongo_db]
            
            # Customer profiles collection
            await db.customer_profiles.create_index("customer_id", unique=True)
            await db.customer_profiles.create_index("email")
            await db.customer_profiles.create_index("updated_at")
            
            # Visual analyses collection
            await db.visual_analyses.create_index("customer_id")
            await db.visual_analyses.create_index("timestamp")
            await db.visual_analyses.create_index("equipment_type")
            
            # Communication analyses collection
            await db.communication_analyses.create_index("customer_id")
            await db.communication_analyses.create_index("timestamp")
            await db.communication_analyses.create_index("analysis_type")
            
            # Raw email data collection
            await db.raw_emails.create_index("customer_id")
            await db.raw_emails.create_index("timestamp")
            await db.raw_emails.create_index("sender")
            
            # Transcription data collection
            await db.transcriptions.create_index("customer_id")
            await db.transcriptions.create_index("timestamp")
            await db.transcriptions.create_index("confidence_score")
            
            logger.info("✅ MongoDB indexes created/verified")
        except Exception as e:
            logger.error(f"❌ Error creating MongoDB indexes: {e}")
    
    @asynccontextmanager
    async def get_postgres_connection(self):
        """Get PostgreSQL connection from pool"""
        conn = None
        try:
            conn = self.postgres_pool.getconn()
            yield conn
        finally:
            if conn:
                self.postgres_pool.putconn(conn)
    
    async def get_mongo_db(self):
        """Get MongoDB database (async)"""
        return self.mongo_async_client[self.config.mongo_db]
    
    async def cache_customer_data(self, customer_id: str, data: Dict[str, Any], ttl: int = 3600):
        """Cache customer data in Redis"""
        try:
            import json
            cache_key = f"customer:{customer_id}"
            cache_data = json.dumps(data, default=str)
            await self.redis_client.setex(cache_key, ttl, cache_data)
            logger.debug(f"✅ Cached data for customer {customer_id}")
        except Exception as e:
            logger.error(f"❌ Error caching customer data: {e}")
    
    async def get_cached_customer_data(self, customer_id: str) -> Optional[Dict[str, Any]]:
        """Get cached customer data from Redis"""
        try:
            import json
            cache_key = f"customer:{customer_id}"
            cached_data = await self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.error(f"❌ Error getting cached customer data: {e}")
            return None
    
    async def store_visual_analysis(self, customer_id: str, analysis_data: Dict[str, Any]):
        """Store visual analysis in both MongoDB and PostgreSQL"""
        try:
            # MongoDB - full analysis data
            mongo_db = await self.get_mongo_db()
            await mongo_db.visual_analyses.insert_one({
                'customer_id': customer_id,
                'analysis_data': analysis_data,
                'timestamp': analysis_data.get('analysis_timestamp'),
                'processed_by': 'gemma_vision'
            })
            
            # PostgreSQL - structured data
            async with self.get_postgres_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        INSERT INTO equipment_visual_analyses 
                        (customer_id, image_path, equipment_type, brand, model, 
                         condition_assessment, issues_detected, maintenance_recommendations,
                         confidence_score, processed_tokens, analysis_timestamp)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        customer_id,
                        analysis_data.get('image_path'),
                        analysis_data.get('equipment_type'),
                        analysis_data.get('brand'),
                        analysis_data.get('model'),
                        analysis_data.get('condition_assessment'),
                        json.dumps(analysis_data.get('issues_detected', [])),
                        json.dumps(analysis_data.get('maintenance_recommendations', [])),
                        analysis_data.get('confidence_score'),
                        analysis_data.get('processed_tokens'),
                        analysis_data.get('analysis_timestamp')
                    ))
                conn.commit()
            
            logger.info(f"✅ Stored visual analysis for customer {customer_id}")
            
        except Exception as e:
            logger.error(f"❌ Error storing visual analysis: {e}")
    
    async def get_customer_equipment_history(self, customer_id: str) -> List[Dict[str, Any]]:
        """Get complete equipment history for customer"""
        try:
            async with self.get_postgres_connection() as conn:
                with conn.cursor() as cur:
                    cur.execute("""
                        SELECT * FROM equipment_visual_analyses 
                        WHERE customer_id = %s 
                        ORDER BY analysis_timestamp DESC
                    """, (customer_id,))
                    
                    columns = [desc[0] for desc in cur.description]
                    results = []
                    for row in cur.fetchall():
                        results.append(dict(zip(columns, row)))
                    
                    return results
        except Exception as e:
            logger.error(f"❌ Error getting equipment history: {e}")
            return []
    
    async def close_all(self):
        """Close all database connections"""
        try:
            if self.postgres_pool:
                self.postgres_pool.closeall()
            if self.mongo_client:
                self.mongo_client.close()
            if self.mongo_async_client:
                self.mongo_async_client.close()
            if self.redis_client:
                await self.redis_client.close()
            logger.info("✅ All database connections closed")
        except Exception as e:
            logger.error(f"❌ Error closing connections: {e}")


# Global database manager instance
db_manager = EnhancedDatabaseManager()


async def initialize_databases():
    """Initialize all databases"""
    await db_manager.initialize_all()


async def demo_database_integration():
    """Demo the enhanced database integration"""
    logger.info("🚀 Testing Enhanced Database Integration")
    
    try:
        # Initialize databases
        await initialize_databases()
        
        # Test customer data operations
        customer_id = "test_customer_001"
        
        # Test caching
        test_data = {
            "name": "Jan Kowalski",
            "email": "<EMAIL>",
            "equipment_count": 3,
            "last_service": "2024-11-15"
        }
        
        await db_manager.cache_customer_data(customer_id, test_data)
        cached_data = await db_manager.get_cached_customer_data(customer_id)
        
        print(f"✅ Cache test: {cached_data is not None}")
        
        # Test visual analysis storage
        analysis_data = {
            "image_path": "/test/image.jpg",
            "equipment_type": "klimatyzacja",
            "brand": "LG",
            "model": "Artcool",
            "condition_assessment": "good",
            "issues_detected": ["minor dust buildup"],
            "maintenance_recommendations": ["filter replacement"],
            "confidence_score": 0.95,
            "processed_tokens": 256,
            "analysis_timestamp": "2024-12-01T10:00:00"
        }
        
        await db_manager.store_visual_analysis(customer_id, analysis_data)
        
        # Get equipment history
        history = await db_manager.get_customer_equipment_history(customer_id)
        print(f"✅ Equipment history: {len(history)} records")
        
        print("✅ Database integration test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Database integration test failed: {e}")
    finally:
        await db_manager.close_all()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(demo_database_integration())