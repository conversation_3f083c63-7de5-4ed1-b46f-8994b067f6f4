#!/usr/bin/env python3
"""
🚀 Launch Script for Enhanced Cosmic AI Mixer 2025
Quick launcher with system checks and configuration
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from loguru import logger

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 11):
        logger.error("❌ Python 3.11 or higher is required!")
        logger.info("Current version: {}.{}.{}".format(*sys.version_info[:3]))
        return False
    logger.success("✅ Python version check passed")
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'gradio',
        'loguru', 
        'pandas',
        'plotly',
        'aiohttp',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.success(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            logger.warning(f"⚠️ {package} is missing")
    
    if missing_packages:
        logger.error(f"❌ Missing packages: {', '.join(missing_packages)}")
        logger.info("Run: pip install -r requirements_enhanced_2025.txt")
        return False
    
    logger.success("✅ All core dependencies are installed")
    return True

def check_optional_dependencies():
    """Check optional AI framework dependencies"""
    optional_frameworks = {
        'crewai': 'CrewAI',
        'pydantic': 'PydanticAI', 
        'langchain': 'LangChain'
    }
    
    available_frameworks = []
    
    for package, name in optional_frameworks.items():
        try:
            __import__(package)
            available_frameworks.append(name)
            logger.success(f"✅ {name} is available")
        except ImportError:
            logger.warning(f"⚠️ {name} is not installed (optional)")
    
    if available_frameworks:
        logger.info(f"🤖 Available AI frameworks: {', '.join(available_frameworks)}")
    else:
        logger.warning("⚠️ No optional AI frameworks installed")
    
    return True

def check_services():
    """Check if external services are running"""
    services_status = {}
    
    # Check Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        r.ping()
        services_status['Redis'] = True
        logger.success("✅ Redis is running")
    except Exception:
        services_status['Redis'] = False
        logger.warning("⚠️ Redis is not running (optional)")
    
    # Check transcription service
    try:
        import requests
        response = requests.get("http://localhost:8765/health", timeout=2)
        if response.status_code == 200:
            services_status['Transcription'] = True
            logger.success("✅ Transcription service is running")
        else:
            services_status['Transcription'] = False
            logger.warning("⚠️ Transcription service is not responding")
    except Exception:
        services_status['Transcription'] = False
        logger.warning("⚠️ Transcription service is not running (optional)")
    
    return services_status

def setup_environment():
    """Setup environment variables if needed"""
    env_file = Path(".env")
    
    if not env_file.exists():
        logger.warning("⚠️ .env file not found")
        
        # Create basic .env file
        env_content = """# Enhanced Cosmic AI Mixer 2025 Configuration

# Debug Settings
DEBUG=true
LOG_LEVEL=INFO

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=7860

# AI Framework Configuration (optional)
# OPENAI_API_KEY=your_openai_key_here
# ANTHROPIC_API_KEY=your_anthropic_key_here

# Database Configuration (optional)
# REDIS_URL=redis://localhost:6379
# POSTGRES_URL=postgresql://user:pass@localhost/db

# Transcription Service (optional)
# NEMO_SERVICE_URL=http://localhost:8765

# System Configuration
CACHE_TTL=3600
MAX_WORKERS=4
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        logger.success("✅ Created basic .env file")
        logger.info("📝 Please edit .env file with your configuration")
    else:
        logger.success("✅ .env file found")
    
    return True

def display_banner():
    """Display startup banner"""
    banner = """
🌟 ═══════════════════════════════════════════════════════════════ 🌟
    
    ███████╗███╗   ██╗██╗  ██╗ █████╗ ███╗   ██╗ ██████╗███████╗██████╗ 
    ██╔════╝████╗  ██║██║  ██║██╔══██╗████╗  ██║██╔════╝██╔════╝██╔══██╗
    █████╗  ██╔██╗ ██║███████║███████║██╔██╗ ██║██║     █████╗  ██║  ██║
    ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║╚██╗██║██║     ██╔══╝  ██║  ██║
    ███████╗██║ ╚████║██║  ██║██║  ██║██║ ╚████║╚██████╗███████╗██████╔╝
    ╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝ ╚═════╝╚══════╝╚═════╝ 
    
         ██████╗ ██████╗ ███████╗███╗   ███╗██╗ ██████╗    ██████╗  ██████╗ ██████╗ ███████╗
        ██╔════╝██╔═══██╗██╔════╝████╗ ████║██║██╔════╝   ██╔══██╗██╔═████╗╚════██╗██╔════╝
        ██║     ██║   ██║███████╗██╔████╔██║██║██║        ██████╔╝██║██╔██║ █████╔╝███████╗
        ██║     ██║   ██║╚════██║██║╚██╔╝██║██║██║        ██╔══██╗████╔╝██║██╔═══╝ ╚════██║
        ╚██████╗╚██████╔╝███████║██║ ╚═╝ ██║██║╚██████╗   ██║  ██║╚██████╔╝███████╗███████║
         ╚═════╝ ╚═════╝ ╚══════╝╚═╝     ╚═╝╚═╝ ╚═════╝   ╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚══════╝
    
    🌟 Enhanced Cosmic AI Mixer 2025 🌟
    Super Advanced Python Mixer with Tavily Integration & Modern AI Frameworks
    
🌟 ═══════════════════════════════════════════════════════════════ 🌟
"""
    print(banner)

def main():
    """Main launcher function"""
    display_banner()
    
    logger.info("🚀 Starting Enhanced Cosmic AI Mixer 2025 launcher...")
    
    # System checks
    logger.info("🔍 Performing system checks...")
    
    if not check_python_version():
        sys.exit(1)
    
    if not check_dependencies():
        logger.error("❌ Dependency check failed!")
        logger.info("💡 Install dependencies with: pip install -r requirements_enhanced_2025.txt")
        sys.exit(1)
    
    check_optional_dependencies()
    services_status = check_services()
    setup_environment()
    
    # Display system status
    logger.info("📊 System Status Summary:")
    logger.info("=" * 50)
    logger.info("✅ Python: Compatible")
    logger.info("✅ Core Dependencies: Installed")
    logger.info(f"{'✅' if services_status.get('Redis') else '⚠️'} Redis: {'Running' if services_status.get('Redis') else 'Not Running'}")
    logger.info(f"{'✅' if services_status.get('Transcription') else '⚠️'} Transcription: {'Running' if services_status.get('Transcription') else 'Not Running'}")
    logger.info("=" * 50)
    
    # Launch confirmation
    logger.info("🌟 Ready to launch Enhanced Cosmic AI Mixer 2025!")
    logger.info("📍 Interface will be available at: http://localhost:7860")
    
    try:
        # Import and launch the main application
        logger.info("🚀 Launching application...")
        
        # Add current directory to Python path
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        # Import and run the main application
        from enhanced_cosmic_ai_mixer_2025 import main as run_mixer
        
        logger.success("✅ Application imported successfully")
        logger.info("🌐 Starting Gradio interface...")
        
        # Run the application
        run_mixer()
        
    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("💡 Make sure enhanced_cosmic_ai_mixer_2025.py is in the current directory")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        logger.info("💡 Check the logs above for more details")
        sys.exit(1)

if __name__ == "__main__":
    main()
