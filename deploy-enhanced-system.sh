#!/bin/bash

# Enhanced HVAC CRM System Deployment Script

# Start the Python Mixer MCP server
echo "🚀 Starting Python Mixer MCP server..."
cd /home/<USER>/HVAC/unifikacja/python_mixer
docker build -t python-mixer-mcp -f Dockerfile.mcp . && \
docker run -d --name python-mixer-mcp -p 8052:8052 python-mixer-mcp
echo "✅ Python Mixer MCP server started on port 8052"

# Start the main HVAC Remix application
echo "🚀 Starting HVAC Remix..."
cd /home/<USER>/HVAC/unifikacja/hvac-remix
npm run dev &
echo "✅ HVAC Remix started"

# Start the GoSpine backend
echo "🚀 Starting GoSpine Backend..."
cd /home/<USER>/HVAC/unifikacja/GoSpine
go run main.go &
echo "✅ GoSpine backend started"

# Start monitoring services
echo "🚀 Starting Monitoring Services..."
cd /home/<USER>/HVAC/unifikacja/monitoring
docker-compose up -d
echo "✅ Monitoring services started"

echo "🌈 Deployment completed successfully!"
