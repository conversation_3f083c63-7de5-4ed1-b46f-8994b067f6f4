# 🚀 Enhanced Email Processing Configuration
# Konfiguracja usprawnionych przepływów informacji po przetworzeniu maili

# Główne ustawienia Enhanced Email Service
enhanced_email_service:
  enable_enhanced_processing: true
  enable_validation: true
  enable_error_recovery: true
  processing_timeout: "5m"
  max_concurrent_processing: 10
  enable_detailed_logging: true
  enable_metrics_collection: true

# 🔄 Enhanced Post-Processing Pipeline Configuration
post_processing:
  enable_parallel_processing: true
  max_concurrent_stages: 3
  default_timeout: "60s"
  default_retries: 3
  quality_threshold: 0.8
  confidence_threshold: 0.7
  enable_quality_gates: true
  state_retention_period: "24h"
  metrics_retention_period: "168h" # 7 days

  # Processing Stages Configuration
  stages:
    data_validation:
      enabled: true
      timeout: "30s"
      retries: 2
      required: true
      parallel: false
      
    ai_enhancement:
      enabled: true
      timeout: "60s"
      retries: 3
      required: false
      parallel: true
      
    customer_matching:
      enabled: true
      timeout: "45s"
      retries: 2
      required: false
      parallel: true
      
    workflow_trigger:
      enabled: true
      timeout: "30s"
      retries: 3
      required: false
      parallel: false
      
    data_persistence:
      enabled: true
      timeout: "60s"
      retries: 3
      required: true
      parallel: false

  # Quality Gates Configuration
  quality_gates:
    data_completeness:
      enabled: true
      min_quality_score: 0.8
      min_confidence: 0.7
      auto_approve: false
      manual_review: true
      required_fields:
        - "email_id"
        - "source_account"
        - "processing_type"
        
    ai_confidence:
      enabled: true
      min_quality_score: 0.7
      min_confidence: 0.8
      auto_approve: true
      manual_review: false
      
    business_rules:
      enabled: true
      min_quality_score: 0.9
      min_confidence: 0.8
      auto_approve: false
      manual_review: true

# 🔍 Unified Validation Pipeline Configuration
validation:
  enable_syntactic_validation: true
  enable_semantic_validation: true
  enable_business_validation: true
  enable_caching: true
  cache_ttl: "1h"
  quality_threshold: 0.8
  confidence_threshold: 0.7
  max_validation_time: "30s"
  parallel_validation: true
  strict_mode: false

  # Syntactic Validators
  syntactic_validators:
    email_structure:
      enabled: true
      weight: 0.3
      
    data_format:
      enabled: true
      weight: 0.3
      
    field_presence:
      enabled: true
      weight: 0.4

  # Semantic Validators
  semantic_validators:
    content_coherence:
      enabled: true
      weight: 0.4
      
    ai_confidence:
      enabled: true
      weight: 0.3
      
    data_consistency:
      enabled: true
      weight: 0.3

  # Business Validators
  business_validators:
    hvac_business_rules:
      enabled: true
      weight: 0.4
      
    workflow_compliance:
      enabled: true
      weight: 0.3
      
    customer_data_rules:
      enabled: true
      weight: 0.3

  # Validation Rules
  validation_rules:
    required_fields_by_type:
      customer_email:
        - "email_id"
        - "source_account"
        - "processing_type"
        - "analysis"
      transcription_email:
        - "email_id"
        - "source_account"
        - "processing_type"
        - "transcription"
      mixed:
        - "email_id"
        - "source_account"
        - "processing_type"

# 🔧 Intelligent Error Recovery Configuration
error_recovery:
  enable_auto_recovery: true
  enable_pattern_detection: true
  enable_ml_classification: false # Disabled by default
  max_recovery_attempts: 3
  recovery_timeout: "2m"
  pattern_detection_window: "24h"
  error_retention_period: "168h" # 7 days
  auto_recovery_threshold: 0.8
  notification_threshold: 5
  enable_detailed_logging: true

  # Recovery Strategies Configuration
  recovery_strategies:
    database_reconnect:
      enabled: true
      confidence_threshold: 0.9
      max_attempts: 3
      backoff_strategy: "exponential"
      
    redis_reconnect:
      enabled: true
      confidence_threshold: 0.9
      max_attempts: 3
      backoff_strategy: "exponential"
      
    email_processing_retry:
      enabled: true
      confidence_threshold: 0.8
      max_attempts: 2
      backoff_strategy: "linear"
      
    ai_service_recovery:
      enabled: true
      confidence_threshold: 0.7
      max_attempts: 3
      backoff_strategy: "exponential"
      
    data_corruption_recovery:
      enabled: true
      confidence_threshold: 0.6
      max_attempts: 1
      backoff_strategy: "none"
      
    circuit_breaker_reset:
      enabled: true
      confidence_threshold: 0.8
      max_attempts: 2
      backoff_strategy: "linear"

  # Error Pattern Detection
  pattern_detection:
    enable_frequency_analysis: true
    enable_time_series_analysis: true
    enable_correlation_analysis: true
    min_pattern_frequency: 3
    pattern_time_window: "1h"
    correlation_threshold: 0.7

  # Error Classification
  error_classification:
    categories:
      - "database_error"
      - "network_error"
      - "ai_service_error"
      - "validation_error"
      - "business_logic_error"
      - "data_corruption_error"
      - "timeout_error"
      - "authentication_error"
      - "authorization_error"
      - "unknown_error"
    
    severity_levels:
      - "low"
      - "medium"
      - "high"
      - "critical"

# 📊 Metrics and Monitoring Configuration
metrics:
  enable_collection: true
  collection_interval: "1m"
  retention_period: "168h" # 7 days
  enable_redis_storage: true
  enable_detailed_metrics: true
  
  # Metrics Categories
  categories:
    - "processing_performance"
    - "validation_results"
    - "error_recovery"
    - "quality_scores"
    - "stage_performance"
    - "business_metrics"

# 🔄 Background Services Configuration
background_services:
  metrics_collector:
    enabled: true
    interval: "1m"
    batch_size: 100
    
  state_cleanup:
    enabled: true
    interval: "1h"
    retention_period: "24h"
    
  pattern_detection:
    enabled: true
    interval: "5m"
    analysis_window: "1h"
    
  error_analysis:
    enabled: true
    interval: "10m"
    batch_size: 50

# 🔗 Integration Configuration
integration:
  # Redis Configuration
  redis:
    enable_persistence: true
    key_prefix: "enhanced_email:"
    default_ttl: "24h"
    
  # Database Configuration
  database:
    enable_audit_trail: true
    enable_performance_logging: true
    
  # External Services
  external_services:
    ai_services:
      timeout: "30s"
      retries: 3
      circuit_breaker_enabled: true
      
    notification_services:
      timeout: "10s"
      retries: 2
      
    workflow_services:
      timeout: "60s"
      retries: 3

# 🚨 Alerting and Notifications
alerting:
  enable_alerts: true
  
  # Alert Thresholds
  thresholds:
    error_rate: 0.05 # 5%
    processing_time: "5m"
    quality_score: 0.7
    recovery_failure_rate: 0.1 # 10%
    
  # Notification Channels
  channels:
    email:
      enabled: true
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
    
    slack:
      enabled: false
      webhook_url: ""
      
    sms:
      enabled: false

# 🔒 Security Configuration
security:
  enable_data_encryption: true
  enable_audit_logging: true
  enable_access_control: true
  
  # Data Protection
  data_protection:
    encrypt_sensitive_fields: true
    mask_personal_data: true
    enable_data_retention_policies: true
    
  # Access Control
  access_control:
    enable_role_based_access: true
    enable_api_key_validation: true
    enable_rate_limiting: true
