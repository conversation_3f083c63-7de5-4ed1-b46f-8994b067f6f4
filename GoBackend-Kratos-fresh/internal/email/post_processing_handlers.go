package email

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🔧 Stage Handlers Implementation

// DataValidationStageHandler validates processed email data
type DataValidationStageHandler struct {
	log *log.Helper
}

func (h *DataValidationStageHandler) Execute(ctx context.Context, result *ProcessingResult) (*StageResult, error) {
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Info("🔍 Executing data validation stage...")
	
	stageResult := &StageResult{
		StageID:      "data_validation",
		Success:      true,
		Data:         make(map[string]interface{}),
		Metadata:     make(map[string]interface{}),
		QualityScore: 1.0,
		Confidence:   1.0,
		Errors:       []string{},
		Warnings:     []string{},
	}
	
	// Validate basic fields
	if result.EmailID == "" {
		stageResult.Errors = append(stageResult.Errors, "Email ID is required")
		stageResult.Success = false
		stageResult.QualityScore -= 0.3
	}
	
	if result.SourceAccount == "" {
		stageResult.Errors = append(stageResult.Errors, "Source account is required")
		stageResult.Success = false
		stageResult.QualityScore -= 0.3
	}
	
	if result.ProcessingType == "" {
		stageResult.Errors = append(stageResult.Errors, "Processing type is required")
		stageResult.Success = false
		stageResult.QualityScore -= 0.3
	}
	
	// Validate analysis data if present
	if result.Analysis != nil {
		if result.Analysis.Category == "" {
			stageResult.Warnings = append(stageResult.Warnings, "Analysis category is missing")
			stageResult.QualityScore -= 0.1
		}
		if result.Analysis.Priority == "" {
			stageResult.Warnings = append(stageResult.Warnings, "Analysis priority is missing")
			stageResult.QualityScore -= 0.1
		}
	}
	
	// Validate transcription data if present
	if result.Transcription != nil {
		if len(result.Transcription.Transcripts) == 0 {
			stageResult.Warnings = append(stageResult.Warnings, "No transcripts found")
			stageResult.QualityScore -= 0.2
		}
	}
	
	// Set metadata
	stageResult.Metadata["validation_rules_applied"] = []string{
		"required_fields", "analysis_completeness", "transcription_completeness",
	}
	stageResult.Metadata["validation_timestamp"] = time.Now().Format(time.RFC3339)
	
	// Adjust confidence based on quality
	if stageResult.QualityScore < 0.8 {
		stageResult.Confidence = stageResult.QualityScore
	}
	
	return stageResult, nil
}

func (h *DataValidationStageHandler) Validate(ctx context.Context, result *ProcessingResult) error {
	if result == nil {
		return fmt.Errorf("processing result is nil")
	}
	return nil
}

func (h *DataValidationStageHandler) Rollback(ctx context.Context, result *ProcessingResult) error {
	// No rollback needed for validation
	return nil
}

// AIEnhancementStageHandler enhances analysis with additional AI insights
type AIEnhancementStageHandler struct {
	log *log.Helper
}

func (h *AIEnhancementStageHandler) Execute(ctx context.Context, result *ProcessingResult) (*StageResult, error) {
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Info("🧠 Executing AI enhancement stage...")
	
	stageResult := &StageResult{
		StageID:      "ai_enhancement",
		Success:      true,
		Data:         make(map[string]interface{}),
		Metadata:     make(map[string]interface{}),
		QualityScore: 0.8,
		Confidence:   0.8,
		Errors:       []string{},
		Warnings:     []string{},
	}
	
	// Enhance analysis with additional insights
	if result.Analysis != nil {
		// Add sentiment analysis enhancement
		stageResult.Data["enhanced_sentiment"] = map[string]interface{}{
			"primary_emotion": "neutral",
			"confidence":      0.8,
			"emotional_indicators": []string{"professional", "urgent"},
		}
		
		// Add intent classification enhancement
		stageResult.Data["enhanced_intent"] = map[string]interface{}{
			"primary_intent":   "service_request",
			"secondary_intent": "information_inquiry",
			"confidence":       0.85,
		}
		
		// Add urgency scoring
		urgencyScore := h.calculateUrgencyScore(result.Analysis)
		stageResult.Data["urgency_score"] = urgencyScore
		stageResult.QualityScore = urgencyScore
	}
	
	// Enhance transcription analysis
	if result.Transcription != nil && len(result.Transcription.Transcripts) > 0 {
		// Add conversation analysis
		stageResult.Data["conversation_analysis"] = map[string]interface{}{
			"speaker_count":     2,
			"conversation_flow": "structured",
			"key_topics":        []string{"hvac_issue", "service_appointment"},
			"action_items":      []string{"schedule_technician", "follow_up_call"},
		}
		
		// Add technical keywords extraction
		keywords := h.extractTechnicalKeywords(result.Transcription.Transcripts)
		stageResult.Data["technical_keywords"] = keywords
	}
	
	stageResult.Metadata["ai_models_used"] = []string{"sentiment_analyzer", "intent_classifier", "keyword_extractor"}
	stageResult.Metadata["enhancement_timestamp"] = time.Now().Format(time.RFC3339)
	
	return stageResult, nil
}

func (h *AIEnhancementStageHandler) calculateUrgencyScore(analysis *EmailAnalysisResult) float64 {
	score := 0.5 // Base score
	
	if analysis.Priority == "urgent" {
		score += 0.4
	} else if analysis.Priority == "high" {
		score += 0.3
	} else if analysis.Priority == "medium" {
		score += 0.1
	}
	
	if analysis.Category == "emergency" {
		score += 0.3
	} else if analysis.Category == "service_request" {
		score += 0.2
	}
	
	if score > 1.0 {
		score = 1.0
	}
	
	return score
}

func (h *AIEnhancementStageHandler) extractTechnicalKeywords(transcripts []string) []string {
	keywords := []string{}
	combinedText := strings.ToLower(strings.Join(transcripts, " "))
	
	technicalTerms := []string{
		"klimatyzacja", "chłodzenie", "grzanie", "wentylacja", "filtr",
		"czynnik", "kompresor", "skraplacz", "parownik", "termostat",
		"awaria", "naprawa", "serwis", "konserwacja", "wymiana",
	}
	
	for _, term := range technicalTerms {
		if strings.Contains(combinedText, term) {
			keywords = append(keywords, term)
		}
	}
	
	return keywords
}

func (h *AIEnhancementStageHandler) Validate(ctx context.Context, result *ProcessingResult) error {
	return nil
}

func (h *AIEnhancementStageHandler) Rollback(ctx context.Context, result *ProcessingResult) error {
	return nil
}

// CustomerMatchingStageHandler matches email to existing customers
type CustomerMatchingStageHandler struct {
	log *log.Helper
}

func (h *CustomerMatchingStageHandler) Execute(ctx context.Context, result *ProcessingResult) (*StageResult, error) {
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Info("👤 Executing customer matching stage...")
	
	stageResult := &StageResult{
		StageID:      "customer_matching",
		Success:      true,
		Data:         make(map[string]interface{}),
		Metadata:     make(map[string]interface{}),
		QualityScore: 0.7,
		Confidence:   0.7,
		Errors:       []string{},
		Warnings:     []string{},
	}
	
	// Mock customer matching logic
	customerMatch := map[string]interface{}{
		"customer_id":    "CUST_12345",
		"customer_name":  "Jan Kowalski",
		"phone_number":   "123-456-789",
		"email":          "<EMAIL>",
		"match_confidence": 0.85,
		"match_method":   "phone_number",
		"is_new_customer": false,
	}
	
	// Check if transcription contains phone number
	if result.Transcription != nil && result.Transcription.CustomerMatch != nil {
		if result.Transcription.CustomerMatch.PhoneNumber != "" {
			customerMatch["phone_number"] = result.Transcription.CustomerMatch.PhoneNumber
			customerMatch["match_method"] = "transcription_phone"
			stageResult.QualityScore = 0.9
			stageResult.Confidence = 0.9
		}
	}
	
	stageResult.Data["customer_match"] = customerMatch
	stageResult.Metadata["matching_algorithms"] = []string{"phone_fuzzy_match", "email_exact_match", "name_similarity"}
	stageResult.Metadata["customer_database_version"] = "v2.1.0"
	
	return stageResult, nil
}

func (h *CustomerMatchingStageHandler) Validate(ctx context.Context, result *ProcessingResult) error {
	return nil
}

func (h *CustomerMatchingStageHandler) Rollback(ctx context.Context, result *ProcessingResult) error {
	return nil
}

// WorkflowTriggerStageHandler triggers appropriate workflows
type WorkflowTriggerStageHandler struct {
	log *log.Helper
}

func (h *WorkflowTriggerStageHandler) Execute(ctx context.Context, result *ProcessingResult) (*StageResult, error) {
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Info("⚡ Executing workflow trigger stage...")
	
	stageResult := &StageResult{
		StageID:      "workflow_trigger",
		Success:      true,
		Data:         make(map[string]interface{}),
		Metadata:     make(map[string]interface{}),
		QualityScore: 0.9,
		Confidence:   0.9,
		Errors:       []string{},
		Warnings:     []string{},
	}
	
	triggeredWorkflows := []map[string]interface{}{}
	
	// Trigger workflows based on analysis
	if result.Analysis != nil {
		if result.Analysis.Priority == "urgent" {
			triggeredWorkflows = append(triggeredWorkflows, map[string]interface{}{
				"workflow_id":   "urgent_email_response",
				"workflow_name": "Urgent Email Response",
				"trigger_reason": "high_priority_email",
				"estimated_completion": "15 minutes",
			})
		}
		
		if result.Analysis.Category == "service_request" {
			triggeredWorkflows = append(triggeredWorkflows, map[string]interface{}{
				"workflow_id":   "service_ticket_creation",
				"workflow_name": "Service Ticket Creation",
				"trigger_reason": "service_request_detected",
				"estimated_completion": "5 minutes",
			})
		}
	}
	
	// Trigger workflows based on transcription
	if result.Transcription != nil && result.Transcription.CallAnalysis != nil {
		if result.Transcription.CallAnalysis.Urgency == "urgent" {
			triggeredWorkflows = append(triggeredWorkflows, map[string]interface{}{
				"workflow_id":   "emergency_dispatch",
				"workflow_name": "Emergency Technician Dispatch",
				"trigger_reason": "urgent_call_detected",
				"estimated_completion": "30 minutes",
			})
		}
	}
	
	stageResult.Data["triggered_workflows"] = triggeredWorkflows
	stageResult.Data["workflow_count"] = len(triggeredWorkflows)
	stageResult.Metadata["workflow_engine_version"] = "v3.2.1"
	stageResult.Metadata["trigger_timestamp"] = time.Now().Format(time.RFC3339)
	
	return stageResult, nil
}

func (h *WorkflowTriggerStageHandler) Validate(ctx context.Context, result *ProcessingResult) error {
	return nil
}

func (h *WorkflowTriggerStageHandler) Rollback(ctx context.Context, result *ProcessingResult) error {
	return nil
}

// DataPersistenceStageHandler persists processed data to database
type DataPersistenceStageHandler struct {
	log *log.Helper
}

func (h *DataPersistenceStageHandler) Execute(ctx context.Context, result *ProcessingResult) (*StageResult, error) {
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Info("💾 Executing data persistence stage...")
	
	stageResult := &StageResult{
		StageID:      "data_persistence",
		Success:      true,
		Data:         make(map[string]interface{}),
		Metadata:     make(map[string]interface{}),
		QualityScore: 0.95,
		Confidence:   0.95,
		Errors:       []string{},
		Warnings:     []string{},
	}
	
	// Mock database persistence
	persistenceResults := map[string]interface{}{
		"email_record_id":        "EMAIL_REC_789",
		"analysis_record_id":     "ANALYSIS_REC_456",
		"transcription_record_id": "TRANS_REC_123",
		"customer_update_id":     "CUST_UPD_321",
		"workflow_trigger_id":    "WF_TRIGGER_654",
	}
	
	// Simulate database operations
	operations := []string{
		"INSERT INTO email_processing_results",
		"UPDATE customer_interactions",
		"INSERT INTO transcription_records",
		"INSERT INTO workflow_triggers",
	}
	
	stageResult.Data["persistence_results"] = persistenceResults
	stageResult.Data["database_operations"] = operations
	stageResult.Metadata["database_version"] = "PostgreSQL 14.2"
	stageResult.Metadata["transaction_id"] = "TXN_" + result.EmailID
	stageResult.Metadata["persistence_timestamp"] = time.Now().Format(time.RFC3339)
	
	return stageResult, nil
}

func (h *DataPersistenceStageHandler) Validate(ctx context.Context, result *ProcessingResult) error {
	return nil
}

func (h *DataPersistenceStageHandler) Rollback(ctx context.Context, result *ProcessingResult) error {
	// Implement rollback logic for database operations
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Warn("🔄 Rolling back data persistence operations...")
	return nil
}
