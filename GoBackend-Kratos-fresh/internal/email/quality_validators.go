package email

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🔍 Quality Validators Implementation

// DataCompletenessValidator validates data completeness
type DataCompletenessValidator struct {
	log *log.Helper
}

func (v *DataCompletenessValidator) Validate(ctx context.Context, result *ProcessingResult, stageResults map[string]*StageResult) (*QualityAssessment, error) {
	log.New<PERSON>elper(log.DefaultLogger).WithContext(ctx).Info("🔍 Validating data completeness...")
	
	assessment := &QualityAssessment{
		Passed:          true,
		Score:           1.0,
		Confidence:      1.0,
		Issues:          []QualityIssue{},
		Recommendations: []string{},
		RequiresReview:  false,
		Metadata:        make(map[string]interface{}),
	}
	
	// Check required fields
	requiredFields := []string{"email_id", "source_account", "processing_type"}
	missingFields := []string{}
	
	if result.EmailID == "" {
		missingFields = append(missingFields, "email_id")
	}
	if result.SourceAccount == "" {
		missingFields = append(missingFields, "source_account")
	}
	if result.ProcessingType == "" {
		missingFields = append(missingFields, "processing_type")
	}
	
	// Penalize for missing required fields
	if len(missingFields) > 0 {
		assessment.Score -= float64(len(missingFields)) * 0.3
		assessment.Issues = append(assessment.Issues, QualityIssue{
			Type:        "missing_required_fields",
			Severity:    "high",
			Description: fmt.Sprintf("Missing required fields: %s", strings.Join(missingFields, ", ")),
			Suggestion:  "Ensure all required fields are populated during processing",
		})
	}
	
	// Check analysis completeness
	if result.Analysis != nil {
		if result.Analysis.Category == "" {
			assessment.Score -= 0.1
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "incomplete_analysis",
				Severity:    "medium",
				Description: "Analysis category is missing",
				Field:       "analysis.category",
				Suggestion:  "Improve AI categorization model",
			})
		}
		
		if result.Analysis.Priority == "" {
			assessment.Score -= 0.1
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "incomplete_analysis",
				Severity:    "medium",
				Description: "Analysis priority is missing",
				Field:       "analysis.priority",
				Suggestion:  "Implement priority scoring algorithm",
			})
		}
		
		if result.Analysis.Confidence < 0.7 {
			assessment.Score -= 0.15
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "low_confidence",
				Severity:    "medium",
				Description: fmt.Sprintf("Analysis confidence is low: %.2f", result.Analysis.Confidence),
				Field:       "analysis.confidence",
				Suggestion:  "Consider manual review for low confidence results",
			})
		}
	} else if result.ProcessingType == "customer_email" {
		assessment.Score -= 0.3
		assessment.Issues = append(assessment.Issues, QualityIssue{
			Type:        "missing_analysis",
			Severity:    "high",
			Description: "Customer email processed without analysis",
			Suggestion:  "Ensure AI analysis is performed for all customer emails",
		})
	}
	
	// Check transcription completeness
	if result.Transcription != nil {
		if len(result.Transcription.Transcripts) == 0 {
			assessment.Score -= 0.2
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "empty_transcription",
				Severity:    "medium",
				Description: "Transcription result contains no transcripts",
				Suggestion:  "Verify STT service functionality",
			})
		}
		
		if result.Transcription.CallAnalysis == nil {
			assessment.Score -= 0.1
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "missing_call_analysis",
				Severity:    "low",
				Description: "Call analysis is missing from transcription",
				Suggestion:  "Implement call analysis for transcribed content",
			})
		}
	}
	
	// Check stage results completeness
	requiredStages := []string{"data_validation"}
	for _, stageName := range requiredStages {
		if stageResult, exists := stageResults[stageName]; !exists || !stageResult.Success {
			assessment.Score -= 0.2
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "failed_required_stage",
				Severity:    "high",
				Description: fmt.Sprintf("Required stage '%s' failed or missing", stageName),
				Suggestion:  "Investigate stage failure and retry if necessary",
			})
		}
	}
	
	// Set overall assessment
	if assessment.Score < 0.8 {
		assessment.Passed = false
		assessment.RequiresReview = true
	}
	
	if assessment.Score < 0.5 {
		assessment.Confidence = assessment.Score
	}
	
	// Add recommendations based on issues
	if len(assessment.Issues) > 0 {
		assessment.Recommendations = append(assessment.Recommendations, 
			"Review and address identified quality issues before proceeding")
	}
	
	if assessment.Score < 0.7 {
		assessment.Recommendations = append(assessment.Recommendations,
			"Consider manual review due to low quality score")
	}
	
	assessment.Metadata["validation_timestamp"] = time.Now().Format(time.RFC3339)
	assessment.Metadata["total_issues"] = len(assessment.Issues)
	assessment.Metadata["missing_fields_count"] = len(missingFields)
	
	return assessment, nil
}

// AIConfidenceValidator validates AI analysis confidence levels
type AIConfidenceValidator struct {
	log *log.Helper
}

func (v *AIConfidenceValidator) Validate(ctx context.Context, result *ProcessingResult, stageResults map[string]*StageResult) (*QualityAssessment, error) {
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Info("🧠 Validating AI confidence levels...")
	
	assessment := &QualityAssessment{
		Passed:          true,
		Score:           1.0,
		Confidence:      1.0,
		Issues:          []QualityIssue{},
		Recommendations: []string{},
		RequiresReview:  false,
		Metadata:        make(map[string]interface{}),
	}
	
	confidenceScores := []float64{}
	
	// Check email analysis confidence
	if result.Analysis != nil {
		confidenceScores = append(confidenceScores, result.Analysis.Confidence)
		
		if result.Analysis.Confidence < 0.7 {
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "low_ai_confidence",
				Severity:    "medium",
				Description: fmt.Sprintf("Email analysis confidence is low: %.2f", result.Analysis.Confidence),
				Field:       "analysis.confidence",
				Suggestion:  "Consider reprocessing with different AI model or manual review",
			})
		}
	}
	
	// Check transcription analysis confidence
	if result.Transcription != nil && result.Transcription.CallAnalysis != nil {
		confidenceScores = append(confidenceScores, result.Transcription.CallAnalysis.Confidence)
		
		if result.Transcription.CallAnalysis.Confidence < 0.7 {
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "low_transcription_confidence",
				Severity:    "medium",
				Description: fmt.Sprintf("Call analysis confidence is low: %.2f", result.Transcription.CallAnalysis.Confidence),
				Field:       "transcription.call_analysis.confidence",
				Suggestion:  "Verify transcription quality and consider manual review",
			})
		}
	}
	
	// Check stage results confidence
	for stageName, stageResult := range stageResults {
		if stageResult.Success && stageResult.Confidence > 0 {
			confidenceScores = append(confidenceScores, stageResult.Confidence)
			
			if stageResult.Confidence < 0.6 {
				assessment.Issues = append(assessment.Issues, QualityIssue{
					Type:        "low_stage_confidence",
					Severity:    "low",
					Description: fmt.Sprintf("Stage '%s' has low confidence: %.2f", stageName, stageResult.Confidence),
					Field:       fmt.Sprintf("stages.%s.confidence", stageName),
					Suggestion:  "Review stage implementation and input data quality",
				})
			}
		}
	}
	
	// Calculate overall confidence
	if len(confidenceScores) > 0 {
		var totalConfidence float64
		for _, score := range confidenceScores {
			totalConfidence += score
		}
		avgConfidence := totalConfidence / float64(len(confidenceScores))
		assessment.Score = avgConfidence
		assessment.Confidence = avgConfidence
		
		// Set pass/fail based on average confidence
		if avgConfidence < 0.8 {
			assessment.Passed = false
		}
		
		if avgConfidence < 0.6 {
			assessment.RequiresReview = true
		}
	} else {
		assessment.Score = 0.5
		assessment.Confidence = 0.5
		assessment.Passed = false
		assessment.Issues = append(assessment.Issues, QualityIssue{
			Type:        "no_confidence_data",
			Severity:    "high",
			Description: "No confidence scores available for validation",
			Suggestion:  "Ensure AI models provide confidence scores",
		})
	}
	
	// Add recommendations
	if assessment.Score < 0.8 {
		assessment.Recommendations = append(assessment.Recommendations,
			"Consider improving AI model training or input data quality")
	}
	
	if len(assessment.Issues) > 2 {
		assessment.Recommendations = append(assessment.Recommendations,
			"Multiple confidence issues detected - comprehensive review recommended")
	}
	
	assessment.Metadata["confidence_scores"] = confidenceScores
	assessment.Metadata["average_confidence"] = assessment.Score
	assessment.Metadata["confidence_threshold"] = 0.8
	assessment.Metadata["validation_timestamp"] = time.Now().Format(time.RFC3339)
	
	return assessment, nil
}

// BusinessRulesValidator validates business logic compliance
type BusinessRulesValidator struct {
	log *log.Helper
}

func (v *BusinessRulesValidator) Validate(ctx context.Context, result *ProcessingResult, stageResults map[string]*StageResult) (*QualityAssessment, error) {
	log.NewHelper(log.DefaultLogger).WithContext(ctx).Info("📋 Validating business rules compliance...")
	
	assessment := &QualityAssessment{
		Passed:          true,
		Score:           1.0,
		Confidence:      1.0,
		Issues:          []QualityIssue{},
		Recommendations: []string{},
		RequiresReview:  false,
		Metadata:        make(map[string]interface{}),
	}
	
	rulesViolated := []string{}
	
	// Business Rule 1: Urgent emails must trigger workflows
	if result.Analysis != nil && result.Analysis.Priority == "urgent" {
		workflowStage, exists := stageResults["workflow_trigger"]
		if !exists || !workflowStage.Success {
			rulesViolated = append(rulesViolated, "urgent_workflow_trigger")
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "business_rule_violation",
				Severity:    "high",
				Description: "Urgent email did not trigger required workflows",
				Suggestion:  "Ensure workflow trigger stage is properly configured",
			})
		}
	}
	
	// Business Rule 2: Service requests must have customer matching
	if result.Analysis != nil && result.Analysis.Category == "service_request" {
		customerStage, exists := stageResults["customer_matching"]
		if !exists || !customerStage.Success {
			rulesViolated = append(rulesViolated, "service_request_customer_matching")
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "business_rule_violation",
				Severity:    "medium",
				Description: "Service request processed without customer matching",
				Suggestion:  "Implement mandatory customer matching for service requests",
			})
		}
	}
	
	// Business Rule 3: Transcriptions must have call analysis
	if result.Transcription != nil && len(result.Transcription.Transcripts) > 0 {
		if result.Transcription.CallAnalysis == nil {
			rulesViolated = append(rulesViolated, "transcription_call_analysis")
			assessment.Issues = append(assessment.Issues, QualityIssue{
				Type:        "business_rule_violation",
				Severity:    "medium",
				Description: "Transcription processed without call analysis",
				Suggestion:  "Ensure call analysis is performed for all transcriptions",
			})
		}
	}
	
	// Business Rule 4: All processing must be persisted
	persistenceStage, exists := stageResults["data_persistence"]
	if !exists || !persistenceStage.Success {
		rulesViolated = append(rulesViolated, "mandatory_data_persistence")
		assessment.Issues = append(assessment.Issues, QualityIssue{
			Type:        "business_rule_violation",
			Severity:    "high",
			Description: "Processing results were not properly persisted",
			Suggestion:  "Investigate data persistence stage failure",
		})
	}
	
	// Business Rule 5: Processing time limits
	if result.ProcessingTime > 5*time.Minute {
		rulesViolated = append(rulesViolated, "processing_time_limit")
		assessment.Issues = append(assessment.Issues, QualityIssue{
			Type:        "business_rule_violation",
			Severity:    "low",
			Description: fmt.Sprintf("Processing time exceeded limit: %v", result.ProcessingTime),
			Suggestion:  "Optimize processing pipeline for better performance",
		})
	}
	
	// Calculate score based on violations
	violationPenalty := float64(len(rulesViolated)) * 0.2
	assessment.Score = 1.0 - violationPenalty
	
	if assessment.Score < 0 {
		assessment.Score = 0
	}
	
	// Set pass/fail status
	if len(rulesViolated) > 0 {
		assessment.Passed = false
	}
	
	if len(rulesViolated) > 2 {
		assessment.RequiresReview = true
	}
	
	// Add recommendations
	if len(rulesViolated) > 0 {
		assessment.Recommendations = append(assessment.Recommendations,
			"Address business rule violations before proceeding")
	}
	
	if assessment.Score < 0.7 {
		assessment.Recommendations = append(assessment.Recommendations,
			"Multiple business rule violations detected - process review required")
	}
	
	assessment.Metadata["rules_violated"] = rulesViolated
	assessment.Metadata["total_violations"] = len(rulesViolated)
	assessment.Metadata["business_rules_version"] = "v2.1.0"
	assessment.Metadata["validation_timestamp"] = time.Now().Format(time.RFC3339)
	
	return assessment, nil
}
