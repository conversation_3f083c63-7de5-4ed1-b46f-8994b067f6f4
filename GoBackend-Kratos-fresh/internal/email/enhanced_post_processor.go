package email

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
)

// 🔄 Enhanced Post-Processing Pipeline
// Centralny system zarządzania wszystkimi krokami po przetworzeniu emaila
type EnhancedPostProcessor struct {
	log         *log.Helper
	redisClient *redis.Client

	// Processing stages
	stages     map[string]ProcessingStage
	stageOrder []string

	// State management
	stateManager *ProcessingStateManager

	// Quality assurance
	qualityGates map[string]QualityGate

	// Metrics and monitoring
	metrics *PostProcessingMetrics

	// Configuration
	config *PostProcessingConfig

	// Lifecycle
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// ProcessingStage represents a stage in post-processing pipeline
type ProcessingStage struct {
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Handler      ProcessingStageHandler `json:"-"`
	Timeout      time.Duration          `json:"timeout"`
	Retries      int                    `json:"retries"`
	Required     bool                   `json:"required"`
	Parallel     bool                   `json:"parallel"`
	Dependencies []string               `json:"dependencies"`
}

// ProcessingStageHandler defines the interface for stage handlers
type ProcessingStageHandler interface {
	Execute(ctx context.Context, result *ProcessingResult) (*StageResult, error)
	Validate(ctx context.Context, result *ProcessingResult) error
	Rollback(ctx context.Context, result *ProcessingResult) error
}

// StageResult represents the result of a processing stage
type StageResult struct {
	StageID        string                 `json:"stage_id"`
	Success        bool                   `json:"success"`
	Data           map[string]interface{} `json:"data"`
	Metadata       map[string]interface{} `json:"metadata"`
	QualityScore   float64                `json:"quality_score"`
	Confidence     float64                `json:"confidence"`
	Errors         []string               `json:"errors,omitempty"`
	Warnings       []string               `json:"warnings,omitempty"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Timestamp      time.Time              `json:"timestamp"`
}

// ProcessingStateManager manages the state of processing pipeline
type ProcessingStateManager struct {
	redisClient *redis.Client
	log         *log.Helper
}

// QualityGate defines quality assurance checkpoints
type QualityGate struct {
	Name            string               `json:"name"`
	MinQualityScore float64              `json:"min_quality_score"`
	MinConfidence   float64              `json:"min_confidence"`
	RequiredFields  []string             `json:"required_fields"`
	Validator       QualityGateValidator `json:"-"`
	AutoApprove     bool                 `json:"auto_approve"`
	ManualReview    bool                 `json:"manual_review"`
}

// QualityGateValidator interface for quality validation
type QualityGateValidator interface {
	Validate(ctx context.Context, result *ProcessingResult, stageResults map[string]*StageResult) (*QualityAssessment, error)
}

// QualityAssessment represents quality assessment result
type QualityAssessment struct {
	Passed          bool                   `json:"passed"`
	Score           float64                `json:"score"`
	Confidence      float64                `json:"confidence"`
	Issues          []QualityIssue         `json:"issues"`
	Recommendations []string               `json:"recommendations"`
	RequiresReview  bool                   `json:"requires_review"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// QualityIssue represents a quality issue
type QualityIssue struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"`
	Description string `json:"description"`
	Field       string `json:"field,omitempty"`
	Suggestion  string `json:"suggestion,omitempty"`
}

// PostProcessingMetrics tracks pipeline performance
type PostProcessingMetrics struct {
	TotalProcessed int64                    `json:"total_processed"`
	SuccessfulRuns int64                    `json:"successful_runs"`
	FailedRuns     int64                    `json:"failed_runs"`
	AverageTime    time.Duration            `json:"average_time"`
	StageMetrics   map[string]*StageMetrics `json:"stage_metrics"`
	QualityMetrics *QualityMetrics          `json:"quality_metrics"`
	mutex          sync.RWMutex
}

// StageMetrics tracks individual stage performance
type StageMetrics struct {
	Executions    int64         `json:"executions"`
	Successes     int64         `json:"successes"`
	Failures      int64         `json:"failures"`
	AverageTime   time.Duration `json:"average_time"`
	LastExecution time.Time     `json:"last_execution"`
}

// QualityMetrics tracks quality assurance metrics
type QualityMetrics struct {
	TotalAssessments  int64   `json:"total_assessments"`
	PassedGates       int64   `json:"passed_gates"`
	FailedGates       int64   `json:"failed_gates"`
	ManualReviews     int64   `json:"manual_reviews"`
	AverageQuality    float64 `json:"average_quality"`
	AverageConfidence float64 `json:"average_confidence"`
}

// PostProcessingConfig configuration for post-processing pipeline
type PostProcessingConfig struct {
	EnableParallelProcessing bool          `yaml:"enable_parallel_processing"`
	MaxConcurrentStages      int           `yaml:"max_concurrent_stages"`
	DefaultTimeout           time.Duration `yaml:"default_timeout"`
	DefaultRetries           int           `yaml:"default_retries"`
	QualityThreshold         float64       `yaml:"quality_threshold"`
	ConfidenceThreshold      float64       `yaml:"confidence_threshold"`
	EnableQualityGates       bool          `yaml:"enable_quality_gates"`
	StateRetentionPeriod     time.Duration `yaml:"state_retention_period"`
	MetricsRetentionPeriod   time.Duration `yaml:"metrics_retention_period"`
}

// NewEnhancedPostProcessor creates a new enhanced post-processor
func NewEnhancedPostProcessor(
	redisClient *redis.Client,
	config *PostProcessingConfig,
	logger log.Logger,
) *EnhancedPostProcessor {
	ctx, cancel := context.WithCancel(context.Background())

	processor := &EnhancedPostProcessor{
		log:          log.NewHelper(logger),
		redisClient:  redisClient,
		stages:       make(map[string]ProcessingStage),
		stageOrder:   []string{},
		qualityGates: make(map[string]QualityGate),
		config:       config,
		ctx:          ctx,
		cancel:       cancel,
		metrics: &PostProcessingMetrics{
			StageMetrics:   make(map[string]*StageMetrics),
			QualityMetrics: &QualityMetrics{},
		},
	}

	processor.stateManager = &ProcessingStateManager{
		redisClient: redisClient,
		log:         processor.log,
	}

	return processor
}

// Initialize initializes the post-processing pipeline
func (p *EnhancedPostProcessor) Initialize() error {
	p.log.Info("🔄 Initializing Enhanced Post-Processing Pipeline...")

	// Register default stages
	p.registerDefaultStages()

	// Register default quality gates
	p.registerDefaultQualityGates()

	// Start background services
	p.startBackgroundServices()

	p.log.Info("✅ Enhanced Post-Processing Pipeline initialized successfully")
	return nil
}

// ProcessEmailResult processes email result through the enhanced pipeline
func (p *EnhancedPostProcessor) ProcessEmailResult(ctx context.Context, result *ProcessingResult) (*EnhancedProcessingResult, error) {
	startTime := time.Now()

	p.log.WithContext(ctx).Infof("🔄 Starting enhanced post-processing for email: %s", result.EmailID)

	// Create enhanced result
	enhancedResult := &EnhancedProcessingResult{
		ProcessingResult:   *result,
		StageResults:       make(map[string]*StageResult),
		QualityAssessments: make(map[string]*QualityAssessment),
		OverallQuality:     0.0,
		OverallConfidence:  0.0,
		ProcessingTime:     0,
		Status:             "processing",
		Timestamp:          time.Now(),
	}

	// Save initial state
	if err := p.stateManager.SaveState(ctx, enhancedResult); err != nil {
		p.log.WithContext(ctx).Warnf("Failed to save initial state: %v", err)
	}

	// Execute processing stages
	if err := p.executeStages(ctx, enhancedResult); err != nil {
		enhancedResult.Status = "failed"
		enhancedResult.Error = err.Error()
		p.updateMetrics(false, time.Since(startTime))
		return enhancedResult, err
	}

	// Execute quality gates
	if p.config.EnableQualityGates {
		if err := p.executeQualityGates(ctx, enhancedResult); err != nil {
			p.log.WithContext(ctx).Warnf("Quality gate failed: %v", err)
			enhancedResult.QualityIssues = append(enhancedResult.QualityIssues, err.Error())
		}
	}

	// Calculate overall metrics
	p.calculateOverallMetrics(enhancedResult)

	// Update status
	enhancedResult.Status = "completed"
	enhancedResult.ProcessingTime = time.Since(startTime)

	// Save final state
	if err := p.stateManager.SaveState(ctx, enhancedResult); err != nil {
		p.log.WithContext(ctx).Warnf("Failed to save final state: %v", err)
	}

	// Update metrics
	p.updateMetrics(true, enhancedResult.ProcessingTime)

	p.log.WithContext(ctx).Infof("✅ Enhanced post-processing completed for email: %s (%.2fs)",
		result.EmailID, enhancedResult.ProcessingTime.Seconds())

	return enhancedResult, nil
}

// EnhancedProcessingResult extends ProcessingResult with enhanced features
type EnhancedProcessingResult struct {
	ProcessingResult
	StageResults       map[string]*StageResult       `json:"stage_results"`
	QualityAssessments map[string]*QualityAssessment `json:"quality_assessments"`
	OverallQuality     float64                       `json:"overall_quality"`
	OverallConfidence  float64                       `json:"overall_confidence"`
	QualityIssues      []string                      `json:"quality_issues"`
	ProcessingTime     time.Duration                 `json:"processing_time"`
	Status             string                        `json:"status"`
	Error              string                        `json:"error,omitempty"`
	Timestamp          time.Time                     `json:"timestamp"`
}

// executeStages executes all processing stages
func (p *EnhancedPostProcessor) executeStages(ctx context.Context, result *EnhancedProcessingResult) error {
	p.log.WithContext(ctx).Info("🔄 Executing processing stages...")

	for _, stageID := range p.stageOrder {
		stage := p.stages[stageID]

		// Check dependencies
		if err := p.checkStageDependencies(stageID, result); err != nil {
			return fmt.Errorf("stage %s dependencies not met: %w", stageID, err)
		}

		// Execute stage
		stageResult, err := p.executeStage(ctx, stage, result)
		if err != nil {
			if stage.Required {
				return fmt.Errorf("required stage %s failed: %w", stageID, err)
			}
			p.log.WithContext(ctx).Warnf("Optional stage %s failed: %v", stageID, err)
		}

		result.StageResults[stageID] = stageResult

		// Update state
		if err := p.stateManager.SaveState(ctx, result); err != nil {
			p.log.WithContext(ctx).Warnf("Failed to save state after stage %s: %v", stageID, err)
		}
	}

	return nil
}

// executeStage executes a single processing stage
func (p *EnhancedPostProcessor) executeStage(ctx context.Context, stage ProcessingStage, result *EnhancedProcessingResult) (*StageResult, error) {
	startTime := time.Now()

	p.log.WithContext(ctx).Infof("🔄 Executing stage: %s", stage.Name)

	// Create timeout context
	stageCtx, cancel := context.WithTimeout(ctx, stage.Timeout)
	defer cancel()

	// Execute with retries
	var stageResult *StageResult
	var err error

	for attempt := 0; attempt <= stage.Retries; attempt++ {
		if attempt > 0 {
			p.log.WithContext(ctx).Infof("Retrying stage %s (attempt %d/%d)", stage.Name, attempt+1, stage.Retries+1)
		}

		stageResult, err = stage.Handler.Execute(stageCtx, &result.ProcessingResult)
		if err == nil {
			break
		}

		if attempt < stage.Retries {
			time.Sleep(time.Duration(attempt+1) * time.Second)
		}
	}

	// Update stage metrics
	p.updateStageMetrics(stage.Name, err == nil, time.Since(startTime))

	if err != nil {
		return &StageResult{
			StageID:        stage.Name,
			Success:        false,
			Errors:         []string{err.Error()},
			ProcessingTime: time.Since(startTime),
			Timestamp:      time.Now(),
		}, err
	}

	stageResult.ProcessingTime = time.Since(startTime)
	stageResult.Timestamp = time.Now()

	p.log.WithContext(ctx).Infof("✅ Stage %s completed successfully (%.2fs)", stage.Name, stageResult.ProcessingTime.Seconds())

	return stageResult, nil
}

// executeQualityGates executes quality gates
func (p *EnhancedPostProcessor) executeQualityGates(ctx context.Context, result *EnhancedProcessingResult) error {
	p.log.WithContext(ctx).Info("🔍 Executing quality gates...")

	for gateName, gate := range p.qualityGates {
		assessment, err := gate.Validator.Validate(ctx, &result.ProcessingResult, result.StageResults)
		if err != nil {
			return fmt.Errorf("quality gate %s failed: %w", gateName, err)
		}

		result.QualityAssessments[gateName] = assessment

		if !assessment.Passed && !gate.AutoApprove {
			if gate.ManualReview {
				p.log.WithContext(ctx).Warnf("Quality gate %s requires manual review", gateName)
				assessment.RequiresReview = true
			} else {
				return fmt.Errorf("quality gate %s failed: score %.2f below threshold %.2f",
					gateName, assessment.Score, gate.MinQualityScore)
			}
		}
	}

	return nil
}

// checkStageDependencies checks if stage dependencies are met
func (p *EnhancedPostProcessor) checkStageDependencies(stageID string, result *EnhancedProcessingResult) error {
	stage := p.stages[stageID]

	for _, depID := range stage.Dependencies {
		if stageResult, exists := result.StageResults[depID]; !exists || !stageResult.Success {
			return fmt.Errorf("dependency %s not satisfied", depID)
		}
	}

	return nil
}

// calculateOverallMetrics calculates overall quality and confidence metrics
func (p *EnhancedPostProcessor) calculateOverallMetrics(result *EnhancedProcessingResult) {
	var totalQuality, totalConfidence float64
	var qualityCount, confidenceCount int

	// Calculate from stage results
	for _, stageResult := range result.StageResults {
		if stageResult.Success {
			if stageResult.QualityScore > 0 {
				totalQuality += stageResult.QualityScore
				qualityCount++
			}
			if stageResult.Confidence > 0 {
				totalConfidence += stageResult.Confidence
				confidenceCount++
			}
		}
	}

	// Calculate from quality assessments
	for _, assessment := range result.QualityAssessments {
		if assessment.Score > 0 {
			totalQuality += assessment.Score
			qualityCount++
		}
		if assessment.Confidence > 0 {
			totalConfidence += assessment.Confidence
			confidenceCount++
		}
	}

	// Calculate averages
	if qualityCount > 0 {
		result.OverallQuality = totalQuality / float64(qualityCount)
	}
	if confidenceCount > 0 {
		result.OverallConfidence = totalConfidence / float64(confidenceCount)
	}
}

// registerDefaultStages registers default processing stages
func (p *EnhancedPostProcessor) registerDefaultStages() {
	// Data validation stage
	p.stages["data_validation"] = ProcessingStage{
		Name:         "data_validation",
		Description:  "Validate processed email data",
		Handler:      &DataValidationStageHandler{},
		Timeout:      30 * time.Second,
		Retries:      2,
		Required:     true,
		Parallel:     false,
		Dependencies: []string{},
	}

	// AI analysis enhancement stage
	p.stages["ai_enhancement"] = ProcessingStage{
		Name:         "ai_enhancement",
		Description:  "Enhance analysis with additional AI insights",
		Handler:      &AIEnhancementStageHandler{},
		Timeout:      60 * time.Second,
		Retries:      3,
		Required:     false,
		Parallel:     true,
		Dependencies: []string{"data_validation"},
	}

	// Customer matching stage
	p.stages["customer_matching"] = ProcessingStage{
		Name:         "customer_matching",
		Description:  "Match email to existing customers",
		Handler:      &CustomerMatchingStageHandler{},
		Timeout:      45 * time.Second,
		Retries:      2,
		Required:     false,
		Parallel:     true,
		Dependencies: []string{"data_validation"},
	}

	// Workflow triggering stage
	p.stages["workflow_trigger"] = ProcessingStage{
		Name:         "workflow_trigger",
		Description:  "Trigger appropriate workflows",
		Handler:      &WorkflowTriggerStageHandler{},
		Timeout:      30 * time.Second,
		Retries:      3,
		Required:     false,
		Parallel:     false,
		Dependencies: []string{"ai_enhancement", "customer_matching"},
	}

	// Data persistence stage
	p.stages["data_persistence"] = ProcessingStage{
		Name:         "data_persistence",
		Description:  "Persist processed data to database",
		Handler:      &DataPersistenceStageHandler{},
		Timeout:      60 * time.Second,
		Retries:      3,
		Required:     true,
		Parallel:     false,
		Dependencies: []string{"workflow_trigger"},
	}

	// Set stage execution order
	p.stageOrder = []string{
		"data_validation",
		"ai_enhancement",
		"customer_matching",
		"workflow_trigger",
		"data_persistence",
	}

	p.log.Infof("✅ Registered %d default processing stages", len(p.stages))
}

// registerDefaultQualityGates registers default quality gates
func (p *EnhancedPostProcessor) registerDefaultQualityGates() {
	// Data completeness gate
	p.qualityGates["data_completeness"] = QualityGate{
		Name:            "data_completeness",
		MinQualityScore: 0.8,
		MinConfidence:   0.7,
		RequiredFields:  []string{"email_id", "source_account", "processing_type"},
		Validator:       &DataCompletenessValidator{},
		AutoApprove:     false,
		ManualReview:    true,
	}

	// AI confidence gate
	p.qualityGates["ai_confidence"] = QualityGate{
		Name:            "ai_confidence",
		MinQualityScore: 0.7,
		MinConfidence:   0.8,
		RequiredFields:  []string{},
		Validator:       &AIConfidenceValidator{},
		AutoApprove:     true,
		ManualReview:    false,
	}

	// Business rules gate
	p.qualityGates["business_rules"] = QualityGate{
		Name:            "business_rules",
		MinQualityScore: 0.9,
		MinConfidence:   0.8,
		RequiredFields:  []string{},
		Validator:       &BusinessRulesValidator{},
		AutoApprove:     false,
		ManualReview:    true,
	}

	p.log.Infof("✅ Registered %d default quality gates", len(p.qualityGates))
}

// startBackgroundServices starts background monitoring services
func (p *EnhancedPostProcessor) startBackgroundServices() {
	// Start metrics collector
	p.wg.Add(1)
	go p.metricsCollector()

	// Start state cleanup service
	p.wg.Add(1)
	go p.stateCleanupService()

	p.log.Info("✅ Background services started")
}

// updateMetrics updates processing metrics
func (p *EnhancedPostProcessor) updateMetrics(success bool, duration time.Duration) {
	p.metrics.mutex.Lock()
	defer p.metrics.mutex.Unlock()

	p.metrics.TotalProcessed++
	if success {
		p.metrics.SuccessfulRuns++
	} else {
		p.metrics.FailedRuns++
	}

	// Update average time
	totalTime := p.metrics.AverageTime * time.Duration(p.metrics.TotalProcessed-1)
	p.metrics.AverageTime = (totalTime + duration) / time.Duration(p.metrics.TotalProcessed)
}

// updateStageMetrics updates stage-specific metrics
func (p *EnhancedPostProcessor) updateStageMetrics(stageName string, success bool, duration time.Duration) {
	p.metrics.mutex.Lock()
	defer p.metrics.mutex.Unlock()

	if p.metrics.StageMetrics[stageName] == nil {
		p.metrics.StageMetrics[stageName] = &StageMetrics{}
	}

	metrics := p.metrics.StageMetrics[stageName]
	metrics.Executions++
	if success {
		metrics.Successes++
	} else {
		metrics.Failures++
	}

	// Update average time
	totalTime := metrics.AverageTime * time.Duration(metrics.Executions-1)
	metrics.AverageTime = (totalTime + duration) / time.Duration(metrics.Executions)
	metrics.LastExecution = time.Now()
}

// SaveState saves processing state to Redis
func (sm *ProcessingStateManager) SaveState(ctx context.Context, result *EnhancedProcessingResult) error {
	stateKey := fmt.Sprintf("email_processing_state:%s", result.EmailID)

	stateData, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal state: %w", err)
	}

	// Save with 24 hour expiration
	err = sm.redisClient.Set(ctx, stateKey, stateData, 24*time.Hour).Err()
	if err != nil {
		return fmt.Errorf("failed to save state to Redis: %w", err)
	}

	sm.log.WithContext(ctx).Infof("💾 Saved processing state for email: %s", result.EmailID)
	return nil
}

// LoadState loads processing state from Redis
func (sm *ProcessingStateManager) LoadState(ctx context.Context, emailID string) (*EnhancedProcessingResult, error) {
	stateKey := fmt.Sprintf("email_processing_state:%s", emailID)

	stateData, err := sm.redisClient.Get(ctx, stateKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to load state from Redis: %w", err)
	}

	var result EnhancedProcessingResult
	err = json.Unmarshal([]byte(stateData), &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal state: %w", err)
	}

	sm.log.WithContext(ctx).Infof("📥 Loaded processing state for email: %s", emailID)
	return &result, nil
}

// metricsCollector runs background metrics collection
func (p *EnhancedPostProcessor) metricsCollector() {
	defer p.wg.Done()

	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			p.log.Info("🔄 Metrics collector shutting down...")
			return
		case <-ticker.C:
			p.collectAndSaveMetrics()
		}
	}
}

// collectAndSaveMetrics collects and saves metrics to Redis
func (p *EnhancedPostProcessor) collectAndSaveMetrics() {
	p.metrics.mutex.RLock()
	metricsData, err := json.Marshal(p.metrics)
	p.metrics.mutex.RUnlock()

	if err != nil {
		p.log.Warnf("Failed to marshal metrics: %v", err)
		return
	}

	// Save metrics to Redis
	metricsKey := "email_processing_metrics"
	err = p.redisClient.Set(p.ctx, metricsKey, metricsData, time.Hour).Err()
	if err != nil {
		p.log.Warnf("Failed to save metrics to Redis: %v", err)
		return
	}

	p.log.Debug("📊 Metrics saved to Redis")
}

// stateCleanupService runs background state cleanup
func (p *EnhancedPostProcessor) stateCleanupService() {
	defer p.wg.Done()

	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			p.log.Info("🧹 State cleanup service shutting down...")
			return
		case <-ticker.C:
			p.cleanupOldStates()
		}
	}
}

// cleanupOldStates removes old processing states
func (p *EnhancedPostProcessor) cleanupOldStates() {
	pattern := "email_processing_state:*"
	keys, err := p.redisClient.Keys(p.ctx, pattern).Result()
	if err != nil {
		p.log.Warnf("Failed to get state keys: %v", err)
		return
	}

	cleanedCount := 0
	for _, key := range keys {
		ttl := p.redisClient.TTL(p.ctx, key).Val()
		if ttl < time.Hour {
			// Key will expire soon, let Redis handle it
			continue
		}

		// Check if state is older than retention period
		stateData, err := p.redisClient.Get(p.ctx, key).Result()
		if err != nil {
			continue
		}

		var result EnhancedProcessingResult
		if err := json.Unmarshal([]byte(stateData), &result); err != nil {
			continue
		}

		if time.Since(result.Timestamp) > p.config.StateRetentionPeriod {
			p.redisClient.Del(p.ctx, key)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		p.log.Infof("🧹 Cleaned up %d old processing states", cleanedCount)
	}
}

// GetMetrics returns current processing metrics
func (p *EnhancedPostProcessor) GetMetrics() *PostProcessingMetrics {
	p.metrics.mutex.RLock()
	defer p.metrics.mutex.RUnlock()

	// Create a copy to avoid race conditions
	metricsCopy := &PostProcessingMetrics{
		TotalProcessed: p.metrics.TotalProcessed,
		SuccessfulRuns: p.metrics.SuccessfulRuns,
		FailedRuns:     p.metrics.FailedRuns,
		AverageTime:    p.metrics.AverageTime,
		StageMetrics:   make(map[string]*StageMetrics),
		QualityMetrics: p.metrics.QualityMetrics,
	}

	// Copy stage metrics
	for name, metrics := range p.metrics.StageMetrics {
		metricsCopy.StageMetrics[name] = &StageMetrics{
			Executions:    metrics.Executions,
			Successes:     metrics.Successes,
			Failures:      metrics.Failures,
			AverageTime:   metrics.AverageTime,
			LastExecution: metrics.LastExecution,
		}
	}

	return metricsCopy
}

// Shutdown gracefully shuts down the post-processor
func (p *EnhancedPostProcessor) Shutdown(ctx context.Context) error {
	p.log.Info("🔄 Shutting down Enhanced Post-Processing Pipeline...")

	// Cancel context to stop background services
	p.cancel()

	// Wait for background services to finish
	done := make(chan struct{})
	go func() {
		p.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		p.log.Info("✅ Enhanced Post-Processing Pipeline shut down successfully")
		return nil
	case <-ctx.Done():
		p.log.Warn("⚠️ Shutdown timeout reached")
		return ctx.Err()
	}
}
