package email

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🔧 Syntactic Validators Implementation

// EmailStructureValidator validates email structure and format
type EmailStructureValidator struct {
	log *log.Helper
}

func (v *EmailStructureValidator) ValidateSyntax(ctx context.Context, data interface{}) (*LayerValidationResult, error) {
	startTime := time.Now()
	
	result := &LayerValidationResult{
		LayerName:    "email_structure",
		Valid:        true,
		Score:        1.0,
		Confidence:   1.0,
		Issues:       []ValidationIssue{},
		Metadata:     make(map[string]interface{}),
	}
	
	processingResult, ok := data.(*ProcessingResult)
	if !ok {
		return nil, fmt.Errorf("invalid data type for email structure validation")
	}
	
	// Validate email ID format
	if processingResult.EmailID == "" {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "missing_email_id",
			Type:        "structure",
			Severity:    "high",
			Layer:       "syntactic",
			Field:       "email_id",
			Description: "Email ID is missing",
			Suggestion:  "Ensure email ID is generated during processing",
			Timestamp:   time.Now(),
		})
		result.Score -= 0.3
		result.Valid = false
	} else {
		// Validate email ID format (should be alphanumeric with underscores/hyphens)
		emailIDPattern := regexp.MustCompile(`^[a-zA-Z0-9_-]+$`)
		if !emailIDPattern.MatchString(processingResult.EmailID) {
			result.Issues = append(result.Issues, ValidationIssue{
				ID:          "invalid_email_id_format",
				Type:        "format",
				Severity:    "medium",
				Layer:       "syntactic",
				Field:       "email_id",
				Description: "Email ID contains invalid characters",
				Suggestion:  "Use only alphanumeric characters, underscores, and hyphens",
				Timestamp:   time.Now(),
			})
			result.Score -= 0.1
		}
	}
	
	// Validate source account format
	if processingResult.SourceAccount == "" {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "missing_source_account",
			Type:        "structure",
			Severity:    "high",
			Layer:       "syntactic",
			Field:       "source_account",
			Description: "Source account is missing",
			Suggestion:  "Specify the source email account",
			Timestamp:   time.Now(),
		})
		result.Score -= 0.3
		result.Valid = false
	} else {
		// Validate email format
		emailPattern := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
		if !emailPattern.MatchString(processingResult.SourceAccount) {
			result.Issues = append(result.Issues, ValidationIssue{
				ID:          "invalid_email_format",
				Type:        "format",
				Severity:    "high",
				Layer:       "syntactic",
				Field:       "source_account",
				Description: "Source account is not a valid email address",
				Suggestion:  "Provide a valid email address format",
				Timestamp:   time.Now(),
			})
			result.Score -= 0.2
			result.Valid = false
		}
	}
	
	// Validate processing type
	validProcessingTypes := []string{"customer_email", "transcription_email", "mixed"}
	if processingResult.ProcessingType == "" {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "missing_processing_type",
			Type:        "structure",
			Severity:    "high",
			Layer:       "syntactic",
			Field:       "processing_type",
			Description: "Processing type is missing",
			Suggestion:  "Specify the type of email processing",
			Timestamp:   time.Now(),
		})
		result.Score -= 0.3
		result.Valid = false
	} else {
		validType := false
		for _, validType := range validProcessingTypes {
			if processingResult.ProcessingType == validType {
				validType = true
				break
			}
		}
		if !validType {
			result.Issues = append(result.Issues, ValidationIssue{
				ID:          "invalid_processing_type",
				Type:        "format",
				Severity:    "medium",
				Layer:       "syntactic",
				Field:       "processing_type",
				Description: fmt.Sprintf("Invalid processing type: %s", processingResult.ProcessingType),
				Suggestion:  fmt.Sprintf("Use one of: %s", strings.Join(validProcessingTypes, ", ")),
				Timestamp:   time.Now(),
			})
			result.Score -= 0.2
		}
	}
	
	result.ProcessingTime = time.Since(startTime)
	result.Metadata["validation_rules"] = []string{"email_id_format", "email_format", "processing_type"}
	result.Metadata["valid_processing_types"] = validProcessingTypes
	
	return result, nil
}

func (v *EmailStructureValidator) GetValidatorName() string {
	return "email_structure"
}

// DataFormatValidator validates data format consistency
type DataFormatValidator struct {
	log *log.Helper
}

func (v *DataFormatValidator) ValidateSyntax(ctx context.Context, data interface{}) (*LayerValidationResult, error) {
	startTime := time.Now()
	
	result := &LayerValidationResult{
		LayerName:    "data_format",
		Valid:        true,
		Score:        1.0,
		Confidence:   1.0,
		Issues:       []ValidationIssue{},
		Metadata:     make(map[string]interface{}),
	}
	
	processingResult, ok := data.(*ProcessingResult)
	if !ok {
		return nil, fmt.Errorf("invalid data type for data format validation")
	}
	
	// Validate timestamp format
	if processingResult.ProcessingTime <= 0 {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "invalid_processing_time",
			Type:        "format",
			Severity:    "medium",
			Layer:       "syntactic",
			Field:       "processing_time",
			Description: "Processing time is invalid or missing",
			Suggestion:  "Ensure processing time is properly recorded",
			Timestamp:   time.Now(),
		})
		result.Score -= 0.1
	}
	
	// Validate analysis data format if present
	if processingResult.Analysis != nil {
		if processingResult.Analysis.Confidence < 0 || processingResult.Analysis.Confidence > 1 {
			result.Issues = append(result.Issues, ValidationIssue{
				ID:          "invalid_confidence_range",
				Type:        "format",
				Severity:    "medium",
				Layer:       "syntactic",
				Field:       "analysis.confidence",
				Description: "Confidence value is outside valid range (0-1)",
				Suggestion:  "Ensure confidence values are between 0 and 1",
				Timestamp:   time.Now(),
			})
			result.Score -= 0.15
		}
		
		// Validate category format
		if processingResult.Analysis.Category != "" {
			categoryPattern := regexp.MustCompile(`^[a-z_]+$`)
			if !categoryPattern.MatchString(processingResult.Analysis.Category) {
				result.Issues = append(result.Issues, ValidationIssue{
					ID:          "invalid_category_format",
					Type:        "format",
					Severity:    "low",
					Layer:       "syntactic",
					Field:       "analysis.category",
					Description: "Category format should be lowercase with underscores",
					Suggestion:  "Use lowercase letters and underscores only",
					Timestamp:   time.Now(),
				})
				result.Score -= 0.05
			}
		}
	}
	
	// Validate transcription data format if present
	if processingResult.Transcription != nil {
		if len(processingResult.Transcription.Transcripts) == 0 {
			result.Issues = append(result.Issues, ValidationIssue{
				ID:          "empty_transcripts_array",
				Type:        "format",
				Severity:    "medium",
				Layer:       "syntactic",
				Field:       "transcription.transcripts",
				Description: "Transcripts array is empty",
				Suggestion:  "Ensure transcription contains actual transcript data",
				Timestamp:   time.Now(),
			})
			result.Score -= 0.2
		}
		
		// Validate individual transcripts
		for i, transcript := range processingResult.Transcription.Transcripts {
			if strings.TrimSpace(transcript) == "" {
				result.Issues = append(result.Issues, ValidationIssue{
					ID:          fmt.Sprintf("empty_transcript_%d", i),
					Type:        "format",
					Severity:    "low",
					Layer:       "syntactic",
					Field:       fmt.Sprintf("transcription.transcripts[%d]", i),
					Description: "Transcript is empty or contains only whitespace",
					Suggestion:  "Remove empty transcripts or ensure proper content",
					Timestamp:   time.Now(),
				})
				result.Score -= 0.05
			}
		}
	}
	
	result.ProcessingTime = time.Since(startTime)
	result.Metadata["validation_rules"] = []string{"timestamp_format", "confidence_range", "category_format", "transcript_content"}
	
	return result, nil
}

func (v *DataFormatValidator) GetValidatorName() string {
	return "data_format"
}

// FieldPresenceValidator validates required field presence
type FieldPresenceValidator struct {
	log *log.Helper
}

func (v *FieldPresenceValidator) ValidateSyntax(ctx context.Context, data interface{}) (*LayerValidationResult, error) {
	startTime := time.Now()
	
	result := &LayerValidationResult{
		LayerName:    "field_presence",
		Valid:        true,
		Score:        1.0,
		Confidence:   1.0,
		Issues:       []ValidationIssue{},
		Metadata:     make(map[string]interface{}),
	}
	
	processingResult, ok := data.(*ProcessingResult)
	if !ok {
		return nil, fmt.Errorf("invalid data type for field presence validation")
	}
	
	// Define required fields based on processing type
	requiredFields := map[string][]string{
		"customer_email": {"email_id", "source_account", "processing_type", "analysis"},
		"transcription_email": {"email_id", "source_account", "processing_type", "transcription"},
		"mixed": {"email_id", "source_account", "processing_type"},
	}
	
	fields, exists := requiredFields[processingResult.ProcessingType]
	if !exists {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "unknown_processing_type",
			Type:        "presence",
			Severity:    "high",
			Layer:       "syntactic",
			Field:       "processing_type",
			Description: "Unknown processing type for field validation",
			Suggestion:  "Use a recognized processing type",
			Timestamp:   time.Now(),
		})
		result.Score -= 0.3
		result.Valid = false
	} else {
		// Check required fields
		for _, field := range fields {
			switch field {
			case "email_id":
				if processingResult.EmailID == "" {
					v.addMissingFieldIssue(result, field)
				}
			case "source_account":
				if processingResult.SourceAccount == "" {
					v.addMissingFieldIssue(result, field)
				}
			case "processing_type":
				if processingResult.ProcessingType == "" {
					v.addMissingFieldIssue(result, field)
				}
			case "analysis":
				if processingResult.Analysis == nil {
					v.addMissingFieldIssue(result, field)
				}
			case "transcription":
				if processingResult.Transcription == nil {
					v.addMissingFieldIssue(result, field)
				}
			}
		}
	}
	
	// Check optional but recommended fields
	recommendedFields := []string{"processing_time", "metadata"}
	missingRecommended := []string{}
	
	if processingResult.ProcessingTime <= 0 {
		missingRecommended = append(missingRecommended, "processing_time")
	}
	
	if len(missingRecommended) > 0 {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "missing_recommended_fields",
			Type:        "presence",
			Severity:    "low",
			Layer:       "syntactic",
			Description: fmt.Sprintf("Missing recommended fields: %s", strings.Join(missingRecommended, ", ")),
			Suggestion:  "Consider adding recommended fields for better data quality",
			Timestamp:   time.Now(),
		})
		result.Score -= 0.05 * float64(len(missingRecommended))
	}
	
	result.ProcessingTime = time.Since(startTime)
	result.Metadata["required_fields"] = fields
	result.Metadata["recommended_fields"] = recommendedFields
	result.Metadata["missing_recommended"] = missingRecommended
	
	return result, nil
}

func (v *FieldPresenceValidator) addMissingFieldIssue(result *LayerValidationResult, field string) {
	result.Issues = append(result.Issues, ValidationIssue{
		ID:          fmt.Sprintf("missing_required_field_%s", field),
		Type:        "presence",
		Severity:    "high",
		Layer:       "syntactic",
		Field:       field,
		Description: fmt.Sprintf("Required field '%s' is missing", field),
		Suggestion:  fmt.Sprintf("Ensure '%s' is properly set during processing", field),
		Timestamp:   time.Now(),
	})
	result.Score -= 0.25
	result.Valid = false
}

func (v *FieldPresenceValidator) GetValidatorName() string {
	return "field_presence"
}

// 🧠 Semantic Validators Implementation

// ContentCoherenceValidator validates content coherence and consistency
type ContentCoherenceValidator struct {
	log *log.Helper
}

func (v *ContentCoherenceValidator) ValidateSemantics(ctx context.Context, data interface{}) (*LayerValidationResult, error) {
	startTime := time.Now()
	
	result := &LayerValidationResult{
		LayerName:    "content_coherence",
		Valid:        true,
		Score:        1.0,
		Confidence:   0.8,
		Issues:       []ValidationIssue{},
		Metadata:     make(map[string]interface{}),
	}
	
	processingResult, ok := data.(*ProcessingResult)
	if !ok {
		return nil, fmt.Errorf("invalid data type for content coherence validation")
	}
	
	// Validate analysis coherence
	if processingResult.Analysis != nil {
		coherenceScore := v.validateAnalysisCoherence(processingResult.Analysis, result)
		result.Score *= coherenceScore
	}
	
	// Validate transcription coherence
	if processingResult.Transcription != nil {
		coherenceScore := v.validateTranscriptionCoherence(processingResult.Transcription, result)
		result.Score *= coherenceScore
	}
	
	// Cross-validate analysis and transcription if both present
	if processingResult.Analysis != nil && processingResult.Transcription != nil {
		crossValidationScore := v.crossValidateContent(processingResult.Analysis, processingResult.Transcription, result)
		result.Score *= crossValidationScore
	}
	
	if result.Score < 0.7 {
		result.Valid = false
	}
	
	result.ProcessingTime = time.Since(startTime)
	result.Metadata["coherence_checks"] = []string{"analysis_coherence", "transcription_coherence", "cross_validation"}
	
	return result, nil
}

func (v *ContentCoherenceValidator) validateAnalysisCoherence(analysis *EmailAnalysisResult, result *LayerValidationResult) float64 {
	score := 1.0
	
	// Check if category and priority are coherent
	if analysis.Category == "emergency" && analysis.Priority != "urgent" {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "category_priority_mismatch",
			Type:        "coherence",
			Severity:    "medium",
			Layer:       "semantic",
			Description: "Emergency category should have urgent priority",
			Suggestion:  "Review AI categorization logic",
			Timestamp:   time.Now(),
		})
		score -= 0.2
	}
	
	// Check confidence vs content quality
	if analysis.Confidence > 0.9 && (analysis.Category == "" || analysis.Priority == "") {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "high_confidence_incomplete_analysis",
			Type:        "coherence",
			Severity:    "medium",
			Layer:       "semantic",
			Description: "High confidence but incomplete analysis data",
			Suggestion:  "Review AI model output completeness",
			Timestamp:   time.Now(),
		})
		score -= 0.15
	}
	
	return score
}

func (v *ContentCoherenceValidator) validateTranscriptionCoherence(transcription *TranscriptionResult, result *LayerValidationResult) float64 {
	score := 1.0
	
	// Check if call analysis matches transcript content
	if transcription.CallAnalysis != nil && len(transcription.Transcripts) > 0 {
		combinedTranscript := strings.ToLower(strings.Join(transcription.Transcripts, " "))
		
		// Check for urgency indicators in transcript
		urgencyKeywords := []string{"pilne", "awaria", "nie działa", "emergency", "urgent"}
		hasUrgencyKeywords := false
		for _, keyword := range urgencyKeywords {
			if strings.Contains(combinedTranscript, keyword) {
				hasUrgencyKeywords = true
				break
			}
		}
		
		if transcription.CallAnalysis.Urgency == "urgent" && !hasUrgencyKeywords {
			result.Issues = append(result.Issues, ValidationIssue{
				ID:          "urgency_transcript_mismatch",
				Type:        "coherence",
				Severity:    "medium",
				Layer:       "semantic",
				Description: "Urgent classification without urgency keywords in transcript",
				Suggestion:  "Review urgency detection algorithm",
				Timestamp:   time.Now(),
			})
			score -= 0.2
		}
	}
	
	return score
}

func (v *ContentCoherenceValidator) crossValidateContent(analysis *EmailAnalysisResult, transcription *TranscriptionResult, result *LayerValidationResult) float64 {
	score := 1.0
	
	// Cross-validate urgency/priority
	if analysis.Priority == "urgent" && transcription.CallAnalysis != nil && transcription.CallAnalysis.Urgency != "urgent" {
		result.Issues = append(result.Issues, ValidationIssue{
			ID:          "cross_urgency_mismatch",
			Type:        "coherence",
			Severity:    "low",
			Layer:       "semantic",
			Description: "Email analysis and call analysis have different urgency levels",
			Suggestion:  "Review consistency between email and call analysis",
			Timestamp:   time.Now(),
		})
		score -= 0.1
	}
	
	return score
}

func (v *ContentCoherenceValidator) GetValidatorName() string {
	return "content_coherence"
}
