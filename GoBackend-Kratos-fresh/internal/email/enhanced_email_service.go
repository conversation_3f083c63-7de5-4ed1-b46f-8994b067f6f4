package email

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
)

// 🚀 Enhanced Email Service
// Główny serwis integrujący wszystkie usprawnienia przepływów informacji
type EnhancedEmailService struct {
	log *log.Helper
	
	// Core processors
	dualSourceProcessor *DualSourceEmailProcessor
	
	// Enhanced components
	postProcessor       *EnhancedPostProcessor
	validationPipeline  *UnifiedValidationPipeline
	errorRecovery       *IntelligentErrorRecovery
	
	// Configuration
	config *EnhancedEmailConfig
	
	// Dependencies
	redisClient *redis.Client
}

// EnhancedEmailConfig comprehensive configuration
type EnhancedEmailConfig struct {
	// Post-processing configuration
	PostProcessing *PostProcessingConfig `yaml:"post_processing"`
	
	// Validation configuration
	Validation *ValidationConfig `yaml:"validation"`
	
	// Error recovery configuration
	ErrorRecovery *ErrorRecoveryConfig `yaml:"error_recovery"`
	
	// General settings
	EnableEnhancedProcessing bool          `yaml:"enable_enhanced_processing"`
	EnableValidation         bool          `yaml:"enable_validation"`
	EnableErrorRecovery      bool          `yaml:"enable_error_recovery"`
	ProcessingTimeout        time.Duration `yaml:"processing_timeout"`
	MaxConcurrentProcessing  int           `yaml:"max_concurrent_processing"`
	EnableDetailedLogging    bool          `yaml:"enable_detailed_logging"`
	EnableMetricsCollection  bool          `yaml:"enable_metrics_collection"`
}

// EnhancedProcessingResult comprehensive processing result
type EnhancedProcessingResult struct {
	// Original processing result
	*ProcessingResult
	
	// Enhanced results
	PostProcessingResult *EnhancedProcessingResult `json:"post_processing_result,omitempty"`
	ValidationResult     *ValidationResult         `json:"validation_result,omitempty"`
	ErrorAnalysis        *ErrorAnalysisResult      `json:"error_analysis,omitempty"`
	RecoveryResult       *RecoveryResult           `json:"recovery_result,omitempty"`
	
	// Overall metrics
	OverallQuality    float64       `json:"overall_quality"`
	OverallConfidence float64       `json:"overall_confidence"`
	ProcessingStages  []string      `json:"processing_stages"`
	TotalTime         time.Duration `json:"total_time"`
	
	// Status and metadata
	Status       string                 `json:"status"`
	Issues       []string               `json:"issues,omitempty"`
	Warnings     []string               `json:"warnings,omitempty"`
	Metadata     map[string]interface{} `json:"metadata"`
	Timestamp    time.Time              `json:"timestamp"`
}

// NewEnhancedEmailService creates a new enhanced email service
func NewEnhancedEmailService(
	dualSourceProcessor *DualSourceEmailProcessor,
	redisClient *redis.Client,
	config *EnhancedEmailConfig,
	logger log.Logger,
) *EnhancedEmailService {
	service := &EnhancedEmailService{
		log:                 log.NewHelper(logger),
		dualSourceProcessor: dualSourceProcessor,
		redisClient:         redisClient,
		config:              config,
	}
	
	// Initialize enhanced components if enabled
	if config.EnableEnhancedProcessing {
		service.postProcessor = NewEnhancedPostProcessor(
			redisClient,
			config.PostProcessing,
			logger,
		)
	}
	
	if config.EnableValidation {
		service.validationPipeline = NewUnifiedValidationPipeline(
			redisClient,
			config.Validation,
			logger,
		)
	}
	
	if config.EnableErrorRecovery {
		service.errorRecovery = NewIntelligentErrorRecovery(
			redisClient,
			config.ErrorRecovery,
			logger,
		)
	}
	
	return service
}

// Initialize initializes the enhanced email service
func (s *EnhancedEmailService) Initialize() error {
	s.log.Info("🚀 Initializing Enhanced Email Service...")
	
	// Initialize enhanced components
	if s.config.EnableEnhancedProcessing && s.postProcessor != nil {
		if err := s.postProcessor.Initialize(); err != nil {
			return fmt.Errorf("failed to initialize post processor: %w", err)
		}
	}
	
	if s.config.EnableValidation && s.validationPipeline != nil {
		if err := s.validationPipeline.Initialize(); err != nil {
			return fmt.Errorf("failed to initialize validation pipeline: %w", err)
		}
	}
	
	if s.config.EnableErrorRecovery && s.errorRecovery != nil {
		if err := s.errorRecovery.Initialize(); err != nil {
			return fmt.Errorf("failed to initialize error recovery: %w", err)
		}
	}
	
	s.log.Info("✅ Enhanced Email Service initialized successfully")
	return nil
}

// ProcessEmailWithEnhancements processes email with all enhancements
func (s *EnhancedEmailService) ProcessEmailWithEnhancements(ctx context.Context, emailData *EmailData) (*EnhancedProcessingResult, error) {
	startTime := time.Now()
	
	s.log.WithContext(ctx).Infof("🚀 Starting enhanced email processing for: %s", emailData.EmailID)
	
	// Create enhanced result
	enhancedResult := &EnhancedProcessingResult{
		ProcessingStages: []string{},
		Metadata:         make(map[string]interface{}),
		Timestamp:        time.Now(),
		Status:           "processing",
	}
	
	// Step 1: Core email processing
	s.log.WithContext(ctx).Info("📧 Executing core email processing...")
	enhancedResult.ProcessingStages = append(enhancedResult.ProcessingStages, "core_processing")
	
	coreResult, err := s.dualSourceProcessor.ProcessEmail(ctx, emailData)
	if err != nil {
		return s.handleProcessingError(ctx, enhancedResult, "core_processing", err)
	}
	
	enhancedResult.ProcessingResult = coreResult
	s.log.WithContext(ctx).Info("✅ Core email processing completed")
	
	// Step 2: Enhanced post-processing
	if s.config.EnableEnhancedProcessing && s.postProcessor != nil {
		s.log.WithContext(ctx).Info("🔄 Executing enhanced post-processing...")
		enhancedResult.ProcessingStages = append(enhancedResult.ProcessingStages, "post_processing")
		
		postResult, err := s.postProcessor.ProcessEmailResult(ctx, coreResult)
		if err != nil {
			return s.handleProcessingError(ctx, enhancedResult, "post_processing", err)
		}
		
		enhancedResult.PostProcessingResult = postResult
		enhancedResult.OverallQuality = postResult.OverallQuality
		enhancedResult.OverallConfidence = postResult.OverallConfidence
		s.log.WithContext(ctx).Info("✅ Enhanced post-processing completed")
	}
	
	// Step 3: Unified validation
	if s.config.EnableValidation && s.validationPipeline != nil {
		s.log.WithContext(ctx).Info("🔍 Executing unified validation...")
		enhancedResult.ProcessingStages = append(enhancedResult.ProcessingStages, "validation")
		
		validationResult, err := s.validationPipeline.ValidateEmailProcessingResult(ctx, coreResult)
		if err != nil {
			return s.handleProcessingError(ctx, enhancedResult, "validation", err)
		}
		
		enhancedResult.ValidationResult = validationResult
		
		// Update overall quality based on validation
		if validationResult.OverallScore < enhancedResult.OverallQuality {
			enhancedResult.OverallQuality = validationResult.OverallScore
		}
		
		// Collect validation issues
		for _, issue := range validationResult.Issues {
			if issue.Severity == "high" {
				enhancedResult.Issues = append(enhancedResult.Issues, issue.Description)
			} else {
				enhancedResult.Warnings = append(enhancedResult.Warnings, issue.Description)
			}
		}
		
		s.log.WithContext(ctx).Infof("✅ Unified validation completed (score: %.2f)", validationResult.OverallScore)
	}
	
	// Step 4: Finalize processing
	enhancedResult.TotalTime = time.Since(startTime)
	enhancedResult.Status = "completed"
	
	// Set final metadata
	enhancedResult.Metadata["processing_stages_count"] = len(enhancedResult.ProcessingStages)
	enhancedResult.Metadata["total_processing_time"] = enhancedResult.TotalTime.String()
	enhancedResult.Metadata["enhanced_features_enabled"] = map[string]bool{
		"post_processing": s.config.EnableEnhancedProcessing,
		"validation":      s.config.EnableValidation,
		"error_recovery":  s.config.EnableErrorRecovery,
	}
	
	s.log.WithContext(ctx).Infof("🎉 Enhanced email processing completed for: %s (%.2fs, quality: %.2f)", 
		emailData.EmailID, enhancedResult.TotalTime.Seconds(), enhancedResult.OverallQuality)
	
	return enhancedResult, nil
}

// handleProcessingError handles processing errors with intelligent recovery
func (s *EnhancedEmailService) handleProcessingError(ctx context.Context, result *EnhancedProcessingResult, stage string, err error) (*EnhancedProcessingResult, error) {
	s.log.WithContext(ctx).Errorf("❌ Error in stage %s: %v", stage, err)
	
	result.Status = "error"
	result.Issues = append(result.Issues, fmt.Sprintf("Error in %s: %s", stage, err.Error()))
	
	// Attempt error recovery if enabled
	if s.config.EnableErrorRecovery && s.errorRecovery != nil {
		s.log.WithContext(ctx).Info("🔧 Attempting error recovery...")
		
		errorInfo := &ErrorInfo{
			ErrorID:      fmt.Sprintf("email_processing_%s_%d", stage, time.Now().Unix()),
			Error:        err,
			ErrorMessage: err.Error(),
			ErrorType:    "processing_error",
			Component:    "enhanced_email_service",
			Operation:    stage,
			Context: map[string]interface{}{
				"stage":        stage,
				"email_id":     result.ProcessingResult.EmailID,
				"processing_stages": result.ProcessingStages,
			},
			Timestamp:    time.Now(),
			ProcessingID: result.ProcessingResult.EmailID,
		}
		
		analysisResult, recoveryResult, recoveryErr := s.errorRecovery.AnalyzeAndRecover(ctx, errorInfo)
		if recoveryErr != nil {
			s.log.WithContext(ctx).Warnf("Error recovery failed: %v", recoveryErr)
		}
		
		result.ErrorAnalysis = analysisResult
		result.RecoveryResult = recoveryResult
		
		// If recovery was successful, update status
		if recoveryResult != nil && recoveryResult.Success {
			result.Status = "recovered"
			s.log.WithContext(ctx).Info("✅ Error recovery successful")
		}
	}
	
	return result, err
}

// GetProcessingMetrics returns comprehensive processing metrics
func (s *EnhancedEmailService) GetProcessingMetrics() map[string]interface{} {
	metrics := make(map[string]interface{})
	
	if s.postProcessor != nil {
		metrics["post_processing"] = s.postProcessor.GetMetrics()
	}
	
	if s.errorRecovery != nil {
		metrics["error_recovery"] = s.errorRecovery.GetMetrics()
	}
	
	// Add service-level metrics
	metrics["service_config"] = map[string]interface{}{
		"enhanced_processing_enabled": s.config.EnableEnhancedProcessing,
		"validation_enabled":          s.config.EnableValidation,
		"error_recovery_enabled":      s.config.EnableErrorRecovery,
		"processing_timeout":          s.config.ProcessingTimeout.String(),
		"max_concurrent_processing":   s.config.MaxConcurrentProcessing,
	}
	
	return metrics
}

// Shutdown gracefully shuts down the enhanced email service
func (s *EnhancedEmailService) Shutdown(ctx context.Context) error {
	s.log.Info("🚀 Shutting down Enhanced Email Service...")
	
	// Shutdown enhanced components
	if s.postProcessor != nil {
		if err := s.postProcessor.Shutdown(ctx); err != nil {
			s.log.Warnf("Failed to shutdown post processor: %v", err)
		}
	}
	
	if s.errorRecovery != nil {
		if err := s.errorRecovery.Shutdown(ctx); err != nil {
			s.log.Warnf("Failed to shutdown error recovery: %v", err)
		}
	}
	
	s.log.Info("✅ Enhanced Email Service shut down successfully")
	return nil
}

// DefaultEnhancedEmailConfig returns default configuration
func DefaultEnhancedEmailConfig() *EnhancedEmailConfig {
	return &EnhancedEmailConfig{
		PostProcessing: &PostProcessingConfig{
			EnableParallelProcessing: true,
			MaxConcurrentStages:     3,
			DefaultTimeout:          60 * time.Second,
			DefaultRetries:          3,
			QualityThreshold:        0.8,
			ConfidenceThreshold:     0.7,
			EnableQualityGates:      true,
			StateRetentionPeriod:    24 * time.Hour,
			MetricsRetentionPeriod:  7 * 24 * time.Hour,
		},
		Validation: &ValidationConfig{
			EnableSyntacticValidation: true,
			EnableSemanticValidation:  true,
			EnableBusinessValidation:  true,
			EnableCaching:             true,
			CacheTTL:                  1 * time.Hour,
			QualityThreshold:          0.8,
			ConfidenceThreshold:       0.7,
			MaxValidationTime:         30 * time.Second,
			ParallelValidation:        true,
			StrictMode:                false,
		},
		ErrorRecovery: &ErrorRecoveryConfig{
			EnableAutoRecovery:       true,
			EnablePatternDetection:   true,
			EnableMLClassification:   false, // Disabled by default
			MaxRecoveryAttempts:      3,
			RecoveryTimeout:          2 * time.Minute,
			PatternDetectionWindow:   24 * time.Hour,
			ErrorRetentionPeriod:     7 * 24 * time.Hour,
			AutoRecoveryThreshold:    0.8,
			NotificationThreshold:    5,
			EnableDetailedLogging:    true,
		},
		EnableEnhancedProcessing: true,
		EnableValidation:         true,
		EnableErrorRecovery:      true,
		ProcessingTimeout:        5 * time.Minute,
		MaxConcurrentProcessing:  10,
		EnableDetailedLogging:    true,
		EnableMetricsCollection:  true,
	}
}
