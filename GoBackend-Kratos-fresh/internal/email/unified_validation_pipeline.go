package email

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-redis/redis/v8"
)

// 🔍 Unified Data Validation Pipeline
// Kompleksowy system walidacji na wszystkich poziomach przepływu danych
type UnifiedValidationPipeline struct {
	log         *log.Helper
	redisClient *redis.Client
	
	// Validation layers
	syntacticValidators map[string]SyntacticValidator
	semanticValidators  map[string]SemanticValidator
	businessValidators  map[string]BusinessValidator
	
	// Validation rules engine
	rulesEngine *ValidationRulesEngine
	
	// Quality scoring
	qualityScorer *QualityScorer
	
	// Validation cache
	validationCache *ValidationCache
	
	// Configuration
	config *ValidationConfig
	
	// Metrics
	metrics *ValidationMetrics
	
	// Lifecycle
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// ValidationResult represents comprehensive validation result
type ValidationResult struct {
	Valid              bool                           `json:"valid"`
	OverallScore       float64                        `json:"overall_score"`
	OverallConfidence  float64                        `json:"overall_confidence"`
	LayerResults       map[string]*LayerValidationResult `json:"layer_results"`
	QualityMetrics     *QualityMetrics                `json:"quality_metrics"`
	Issues             []ValidationIssue              `json:"issues"`
	Recommendations    []string                       `json:"recommendations"`
	ValidationTime     time.Duration                  `json:"validation_time"`
	Timestamp          time.Time                      `json:"timestamp"`
	CacheHit           bool                           `json:"cache_hit"`
}

// LayerValidationResult represents validation result for a specific layer
type LayerValidationResult struct {
	LayerName    string            `json:"layer_name"`
	Valid        bool              `json:"valid"`
	Score        float64           `json:"score"`
	Confidence   float64           `json:"confidence"`
	Issues       []ValidationIssue `json:"issues"`
	Metadata     map[string]interface{} `json:"metadata"`
	ProcessingTime time.Duration   `json:"processing_time"`
}

// ValidationIssue represents a validation issue
type ValidationIssue struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Layer       string                 `json:"layer"`
	Field       string                 `json:"field,omitempty"`
	Description string                 `json:"description"`
	Suggestion  string                 `json:"suggestion,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// Validator interfaces for different validation layers
type SyntacticValidator interface {
	ValidateSyntax(ctx context.Context, data interface{}) (*LayerValidationResult, error)
	GetValidatorName() string
}

type SemanticValidator interface {
	ValidateSemantics(ctx context.Context, data interface{}) (*LayerValidationResult, error)
	GetValidatorName() string
}

type BusinessValidator interface {
	ValidateBusiness(ctx context.Context, data interface{}) (*LayerValidationResult, error)
	GetValidatorName() string
}

// ValidationRulesEngine manages validation rules
type ValidationRulesEngine struct {
	rules       map[string]*ValidationRule
	ruleGroups  map[string][]string
	log         *log.Helper
	redisClient *redis.Client
}

// ValidationRule represents a validation rule
type ValidationRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Layer       string                 `json:"layer"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Condition   string                 `json:"condition"`
	Action      string                 `json:"action"`
	Metadata    map[string]interface{} `json:"metadata"`
	Enabled     bool                   `json:"enabled"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// QualityScorer calculates quality scores
type QualityScorer struct {
	scoringRules map[string]*ScoringRule
	weights      map[string]float64
	log          *log.Helper
}

// ScoringRule defines how to calculate quality scores
type ScoringRule struct {
	Name        string  `json:"name"`
	Weight      float64 `json:"weight"`
	MaxScore    float64 `json:"max_score"`
	MinScore    float64 `json:"min_score"`
	Formula     string  `json:"formula"`
	Description string  `json:"description"`
}

// ValidationCache manages validation result caching
type ValidationCache struct {
	redisClient *redis.Client
	ttl         time.Duration
	log         *log.Helper
}

// ValidationConfig configuration for validation pipeline
type ValidationConfig struct {
	EnableSyntacticValidation bool          `yaml:"enable_syntactic_validation"`
	EnableSemanticValidation  bool          `yaml:"enable_semantic_validation"`
	EnableBusinessValidation  bool          `yaml:"enable_business_validation"`
	EnableCaching             bool          `yaml:"enable_caching"`
	CacheTTL                  time.Duration `yaml:"cache_ttl"`
	QualityThreshold          float64       `yaml:"quality_threshold"`
	ConfidenceThreshold       float64       `yaml:"confidence_threshold"`
	MaxValidationTime         time.Duration `yaml:"max_validation_time"`
	ParallelValidation        bool          `yaml:"parallel_validation"`
	StrictMode                bool          `yaml:"strict_mode"`
}

// ValidationMetrics tracks validation performance
type ValidationMetrics struct {
	TotalValidations     int64                        `json:"total_validations"`
	SuccessfulValidations int64                       `json:"successful_validations"`
	FailedValidations    int64                        `json:"failed_validations"`
	CacheHits            int64                        `json:"cache_hits"`
	CacheMisses          int64                        `json:"cache_misses"`
	AverageValidationTime time.Duration               `json:"average_validation_time"`
	LayerMetrics         map[string]*LayerMetrics     `json:"layer_metrics"`
	QualityDistribution  map[string]int64             `json:"quality_distribution"`
	IssueFrequency       map[string]int64             `json:"issue_frequency"`
	mutex                sync.RWMutex
}

// LayerMetrics tracks layer-specific metrics
type LayerMetrics struct {
	Validations    int64         `json:"validations"`
	Successes      int64         `json:"successes"`
	Failures       int64         `json:"failures"`
	AverageTime    time.Duration `json:"average_time"`
	AverageScore   float64       `json:"average_score"`
	LastValidation time.Time     `json:"last_validation"`
}

// NewUnifiedValidationPipeline creates a new unified validation pipeline
func NewUnifiedValidationPipeline(
	redisClient *redis.Client,
	config *ValidationConfig,
	logger log.Logger,
) *UnifiedValidationPipeline {
	ctx, cancel := context.WithCancel(context.Background())
	
	pipeline := &UnifiedValidationPipeline{
		log:         log.NewHelper(logger),
		redisClient: redisClient,
		syntacticValidators: make(map[string]SyntacticValidator),
		semanticValidators:  make(map[string]SemanticValidator),
		businessValidators:  make(map[string]BusinessValidator),
		config:      config,
		ctx:         ctx,
		cancel:      cancel,
		metrics: &ValidationMetrics{
			LayerMetrics:        make(map[string]*LayerMetrics),
			QualityDistribution: make(map[string]int64),
			IssueFrequency:      make(map[string]int64),
		},
	}
	
	// Initialize components
	pipeline.rulesEngine = &ValidationRulesEngine{
		rules:       make(map[string]*ValidationRule),
		ruleGroups:  make(map[string][]string),
		log:         pipeline.log,
		redisClient: redisClient,
	}
	
	pipeline.qualityScorer = &QualityScorer{
		scoringRules: make(map[string]*ScoringRule),
		weights:      make(map[string]float64),
		log:          pipeline.log,
	}
	
	pipeline.validationCache = &ValidationCache{
		redisClient: redisClient,
		ttl:         config.CacheTTL,
		log:         pipeline.log,
	}
	
	return pipeline
}

// Initialize initializes the validation pipeline
func (p *UnifiedValidationPipeline) Initialize() error {
	p.log.Info("🔍 Initializing Unified Validation Pipeline...")
	
	// Register default validators
	p.registerDefaultValidators()
	
	// Load validation rules
	if err := p.rulesEngine.LoadRules(); err != nil {
		p.log.Warnf("Failed to load validation rules: %v", err)
	}
	
	// Initialize quality scoring
	p.qualityScorer.initializeDefaultRules()
	
	// Start background services
	p.startBackgroundServices()
	
	p.log.Info("✅ Unified Validation Pipeline initialized successfully")
	return nil
}

// ValidateEmailProcessingResult validates email processing result
func (p *UnifiedValidationPipeline) ValidateEmailProcessingResult(ctx context.Context, result *ProcessingResult) (*ValidationResult, error) {
	startTime := time.Now()
	
	p.log.WithContext(ctx).Infof("🔍 Starting unified validation for email: %s", result.EmailID)
	
	// Check cache first
	if p.config.EnableCaching {
		if cachedResult, err := p.validationCache.Get(ctx, result.EmailID); err == nil {
			p.updateMetrics(true, time.Since(startTime), true)
			return cachedResult, nil
		}
	}
	
	// Create validation result
	validationResult := &ValidationResult{
		Valid:         true,
		LayerResults:  make(map[string]*LayerValidationResult),
		Issues:        []ValidationIssue{},
		Recommendations: []string{},
		Timestamp:     time.Now(),
		CacheHit:      false,
	}
	
	// Execute validation layers
	if err := p.executeValidationLayers(ctx, result, validationResult); err != nil {
		p.updateMetrics(false, time.Since(startTime), false)
		return nil, err
	}
	
	// Calculate overall scores
	p.calculateOverallScores(validationResult)
	
	// Generate recommendations
	p.generateRecommendations(validationResult)
	
	// Set final validation time
	validationResult.ValidationTime = time.Since(startTime)
	
	// Cache result if enabled
	if p.config.EnableCaching {
		if err := p.validationCache.Set(ctx, result.EmailID, validationResult); err != nil {
			p.log.WithContext(ctx).Warnf("Failed to cache validation result: %v", err)
		}
	}
	
	// Update metrics
	p.updateMetrics(validationResult.Valid, validationResult.ValidationTime, false)
	
	p.log.WithContext(ctx).Infof("✅ Unified validation completed for email: %s (%.2fs, score: %.2f)", 
		result.EmailID, validationResult.ValidationTime.Seconds(), validationResult.OverallScore)
	
	return validationResult, nil
}

// executeValidationLayers executes all validation layers
func (p *UnifiedValidationPipeline) executeValidationLayers(ctx context.Context, result *ProcessingResult, validationResult *ValidationResult) error {
	// Execute syntactic validation
	if p.config.EnableSyntacticValidation {
		if err := p.executeSyntacticValidation(ctx, result, validationResult); err != nil {
			return fmt.Errorf("syntactic validation failed: %w", err)
		}
	}
	
	// Execute semantic validation
	if p.config.EnableSemanticValidation {
		if err := p.executeSemanticValidation(ctx, result, validationResult); err != nil {
			return fmt.Errorf("semantic validation failed: %w", err)
		}
	}
	
	// Execute business validation
	if p.config.EnableBusinessValidation {
		if err := p.executeBusinessValidation(ctx, result, validationResult); err != nil {
			return fmt.Errorf("business validation failed: %w", err)
		}
	}
	
	return nil
}

// registerDefaultValidators registers default validators for each layer
func (p *UnifiedValidationPipeline) registerDefaultValidators() {
	// Register syntactic validators
	p.syntacticValidators["email_structure"] = &EmailStructureValidator{}
	p.syntacticValidators["data_format"] = &DataFormatValidator{}
	p.syntacticValidators["field_presence"] = &FieldPresenceValidator{}
	
	// Register semantic validators
	p.semanticValidators["content_coherence"] = &ContentCoherenceValidator{}
	p.semanticValidators["ai_confidence"] = &AIConfidenceSemanticValidator{}
	p.semanticValidators["data_consistency"] = &DataConsistencyValidator{}
	
	// Register business validators
	p.businessValidators["hvac_business_rules"] = &HVACBusinessRulesValidator{}
	p.businessValidators["workflow_compliance"] = &WorkflowComplianceValidator{}
	p.businessValidators["customer_data_rules"] = &CustomerDataRulesValidator{}
	
	p.log.Infof("✅ Registered %d syntactic, %d semantic, and %d business validators", 
		len(p.syntacticValidators), len(p.semanticValidators), len(p.businessValidators))
}
